import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Query,
  UseInterceptors,
  HttpStatus,
  UploadedFiles,
  Req,
  UnprocessableEntityException,
  Res,
  Delete,
  HttpException,
} from '@nestjs/common';
import { AgentService } from './agent.service';
import { CreateAgentDto } from './dto/create-agent.dto';
import { UpdateAgentDto } from './dto/update-agent.dto';
import {
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiConsumes,
} from '@nestjs/swagger';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { COMPANY_ENDPOINT } from '../common/constant/apiEndPoints';
import { PageOptionsDto } from '../common/pagination/pageOptions.dto';
import { SortSearchDataDto } from '../common/pagination/sort-search-data.dto';
import { AuditLog } from '../common/decorators/audit-log.decorator';
import { Identifier, UserType } from '@shared-types';
import { Public } from '../common/decorators';
import { GovDocumentType } from './entities/gov-document-type.entity';
import { AgentFilterDto } from './dto/agent-filter.dto';
import { AgentDetail } from './entities/agent.entity';
import { ActivateDeactivateAgentDto } from './dto/activate-deactivate-agent.dto';
import { Response } from 'express';
import { UserId } from '../common/decorators/user-id.decorator';
import { UsersService } from '../users/users.service';
import { AgentExperienceOption } from './entities/agent_experience_option.entity';

@Controller(`${COMPANY_ENDPOINT}agent`)
@ApiTags('agent')
export class AgentController {
  constructor(
    private readonly agentService: AgentService,
    private readonly usersService: UsersService,
  ) {}

  @Post()
  // @Public()
  @ApiOperation({ summary: 'Create new agent' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'profLicenseDoc', maxCount: 1 },
      { name: 'govDocument', maxCount: 1 },
      { name: 'addressProofDocument', maxCount: 1 },
      { name: 'user[profileImageUrl]', maxCount: 1 },
    ]),
  )
  @AuditLog({ objectName: 'agent', operation: 'creation' })
  async createAgent(
    @Body() createAgentDto: CreateAgentDto,
    @Req() request: { user?: UserType },
    @UserId() userId: string,
    @UploadedFiles()
    files: {
      profLicenseDoc?: Express.Multer.File[];
      govDocument?: Express.Multer.File[];
      addressProofDocument?: Express.Multer.File[];
      'user[profileImageUrl]'?: Express.Multer.File[];
    },
  ) {
    // Clean up empty string values to prevent database errors
    if (createAgentDto.marketCenterId === '') {
      createAgentDto.marketCenterId = undefined; // Convert empty string to undefined
    }

    if (createAgentDto.addressProofDocumentTypeId === '') {
      createAgentDto.addressProofDocumentTypeId = undefined;
    }

    if (createAgentDto.driver_license_number === '') {
      createAgentDto.driver_license_number = undefined;
    }

    // Get the user if we have a userId but no request.user
    let user = request.user;
    if (!user && userId) {
      user = await this.usersService.findUserDetailsById(userId);
    }

    // Validate files manually

    const allowedMimeTypes = [
      'image/png',
      'image/jpeg',
      'image/jpg',
      'image/gif',
      'application/pdf',
    ];

    Object.values(files || {}).forEach((fileArray) => {
      fileArray?.forEach((file) => {
        if (!allowedMimeTypes.includes(file.mimetype)) {
          throw new UnprocessableEntityException(
            `Invalid file type. Allowed types are: PNG, JPEG, JPG, GIF, PDF`,
          );
        }
        if (file.size > 10 * 1024 * 1024) {
          throw new UnprocessableEntityException(
            `File size exceeds 10MB limit`,
          );
        }
      });
    });

    // Assign uploaded files to the DTO
    const mergedDto = {
      ...createAgentDto,
      ...(files?.profLicenseDoc?.[0] && {
        profLicenseDoc: files.profLicenseDoc[0],
      }),
      ...(files?.govDocument?.[0] && {
        govDocument: files.govDocument[0],
      }),
      ...(files?.addressProofDocument?.[0] && {
        addressProofDocument: files.addressProofDocument[0],
      }),
      user: {
        ...createAgentDto.user,
        ...(files?.['user[profileImageUrl]']?.[0] && {
          profileImageUrl: files['user[profileImageUrl]'][0],
        }),
      },
    };
    return this.agentService.createAgent(mergedDto, user);
  }

  @Get()
  @ApiOperation({
    summary: 'Get all agents with pagination, sorting and filters',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page',
  })
  @ApiQuery({
    name: 'searchText',
    required: false,
    type: String,
    description: 'Search by name',
  })
  @ApiQuery({
    name: 'sortColumn',
    required: false,
    type: String,
    description: 'Column to sort by',
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction',
  })
  @ApiQuery({
    name: 'isActive',
    required: false,
    type: Boolean,
    description: 'Filter by active status',
  })
  async findAllAgents(@Query() query: any) {
    const pageOptionsDto: PageOptionsDto = {
      page: Number(query.page) || 1,
      limit: Number(query.limit) || 10,
      skip: ((Number(query.page) || 1) - 1) * (Number(query.limit) || 10),
    };

    const sortSearchDataDto: SortSearchDataDto = {
      sortOrder: query.sortOrder || 'DESC',
      sortColumn: query.sortColumn || 'createdAt',
      searchText: query.searchText,
    };

    const filterDto: AgentFilterDto = {
      isActive:
        query.isActive !== undefined ? String(query.isActive) : undefined,
      licenseNumber: query.licenseNumber,
      tinNumber: query.tinNumber,
      state: query.state,
      city: query.city,
      startDate: query.startDate,
      endDate: query.endDate,
      companyName: query.companyName,
      marketCenterName: query.marketCenterName,
    };

    return this.agentService.findAllAgents(
      pageOptionsDto,
      sortSearchDataDto,
      filterDto,
      query.marketCenterId,
      query.accountId,
    );
  }

  @Get('code/:code')
  @Public()
  @ApiOperation({ summary: 'Get agent by code' })
  @ApiParam({ name: 'code', required: true, type: String })
  async findAgentByCode(@Param('code') code: string) {
    return this.agentService.findAgentByCode(code);
  }

  @Get('market-center/:marketCenterId')
  @Public()
  @ApiOperation({ summary: 'Get agents by market center' })
  @ApiParam({ name: 'marketCenterId', required: true, type: String })
  async findAgentsByMarketCenter(
    @Param('marketCenterId') marketCenterId: Identifier,
  ) {
    return this.agentService.findAgentsByMarketCenter(marketCenterId);
  }

  @Patch(':id')
  @Public()
  @ApiOperation({ summary: 'Update agent' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'profLicenseDoc', maxCount: 1 },
      { name: 'govDocument', maxCount: 1 },
      { name: 'addressProofDocument', maxCount: 1 },
      { name: 'user[profileImageUrl]', maxCount: 1 },
    ]),
  )
  @AuditLog({ objectName: 'agent', operation: 'update' })
  async updateAgent(
    @Param('id') id: Identifier,
    @Body() updateAgentDto: UpdateAgentDto,
    @UploadedFiles()
    files: {
      profLicenseDoc?: Express.Multer.File[];
      govDocument?: Express.Multer.File[];
      addressProofDocument?: Express.Multer.File[];
      'user[profileImageUrl]'?: Express.Multer.File[];
    },
  ) {
    // Manual validation for files
    const allowedMimeTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'application/pdf',
    ];

    // Validate each file if present
    Object.entries(files || {}).forEach(([key, fileArray]) => {
      if (fileArray?.[0]) {
        const file = fileArray[0];
        if (!allowedMimeTypes.includes(file.mimetype)) {
          throw new UnprocessableEntityException(
            `Invalid file type for ${key}. Allowed types are: JPG, PNG, and PDF`,
          );
        }
      }
    });

    const mergedDto = {
      ...updateAgentDto,
      ...(files?.profLicenseDoc?.[0] && {
        profLicenseDoc: files.profLicenseDoc[0],
      }),
      ...(files?.govDocument?.[0] && {
        govDocument: files.govDocument[0],
      }),
      ...(files?.addressProofDocument?.[0] && {
        addressProofDocument: files.addressProofDocument[0],
      }),
      user: {
        ...updateAgentDto.user,
        ...(files?.['user[profileImageUrl]']?.[0] && {
          profileImageUrl: files['user[profileImageUrl]'][0],
        }),
      },
    };

    return this.agentService.updateAgent(id, mergedDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Soft delete agent' })
  @ApiParam({ name: 'id', required: true, type: String })
  @AuditLog({ objectName: 'agent', operation: 'deletion' })
  async softDeleteAgent(@Param('id') id: Identifier) {
    const deleted = await this.agentService.softDeleteAgent(id);
    if (!deleted) {
      throw new HttpException('Agent not found', HttpStatus.NOT_FOUND);
    }
    return { message: 'Agent deleted successfully' };
  }

  @Get('gov-document-types')
  @Public()
  @ApiOperation({ summary: 'Get all government document types' })
  async getAllGovDocuments(): Promise<{
    data: GovDocumentType[];
    message: string;
  }> {
    return this.agentService.getAllGovDocuments();
  }

  @Get('address-proof-document-types')
  @Public()
  @ApiOperation({ summary: 'Get all address proof document types' })
  async getAllAddressProofDocuments(): Promise<{
    data: GovDocumentType[];
    message: string;
  }> {
    return this.agentService.getAllAddressProofDocuments();
  }

  @Get('agent-experience-options')
  @Public()
  @ApiOperation({ summary: 'Get all agent experience options' })
  async getAllExperienceOptions(): Promise<{
    data: AgentExperienceOption[];
    message: string;
  }> {
    return this.agentService.getAllExperienceOptions();
  }

  @Get(':id')
  @Public()
  @ApiOperation({ summary: 'Get agent by ID' })
  @ApiParam({ name: 'id', required: true, type: String })
  async findAgentById(@Param('id') id: Identifier) {
    return this.agentService.findAgentById(id);
  }

  @Patch(':id/status')
  @ApiOperation({ summary: 'Change agent status (activate/deactivate)' })
  @AuditLog({ objectName: 'agent', operation: 'status-update' })
  async changeAgentStatus(
    @Param('id') id: string,
    @Body() activateDeactivateAgentDto: ActivateDeactivateAgentDto,
  ): Promise<{ data: AgentDetail; message: string }> {
    return this.agentService.changeAgentStatus(id, activateDeactivateAgentDto);
  }

  @Get('export/excel')
  @ApiOperation({
    summary: 'Export agents to Excel with filters and search',
  })
  async exportMarketCentersToExcel(
    @Query() query: any,
    @Res() response: Response<any, Record<string, any>>,
  ) {
    const sortSearchDataDto: SortSearchDataDto = {
      sortOrder: query.sortOrder || 'DESC',
      sortColumn: query.sortColumn || 'createdAt',
      searchText: query.searchText,
    };

    const filterAgentDto: AgentFilterDto = {
      isActive: query.isActive,
      licenseNumber: query.licenseNumber,
      tinNumber: query.tinNumber,
      zipCode: query.zipCode,
      city: query.city,
      state: query.state,
      startDate: query.startDate,
      endDate: query.endDate,
      ids: query.ids,
    };

    return this.agentService.downloadAgentReport(
      sortSearchDataDto,
      filterAgentDto,
      response,
      query.marketCenterId,
      query.accountId,
    );
  }
}
