import { DataTable } from "../../../../../components/table/data-table";
import { useState } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Avatar, AvatarFallback, AvatarImage } from "@vast-application-framework/ui-components"

const emptyData: any[] = [];

const getColumns = (): ColumnDef<any>[] => [
  {
    header: "S.No.",
    cell: ({ row }) => row.index + 1,
  },
  {
    header: "Agent",
    cell: ({ row }) => {
      const agent = row.original;
      return (
        <div className="flex items-center gap-2">
          <Avatar className="h-8 w-8">
            <AvatarImage src={agent.avatar} />
            <AvatarFallback>{agent.name?.slice(0, 2)}</AvatarFallback>
          </Avatar>
          <div>
            <p className="font-medium">{agent.name}</p>
            <p className="text-xs text-muted-foreground">{agent.email}</p>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "revenue",
    header: "Revenue",
  },
  {
    accessorKey: "commission",
    header: "Commission",
  },
];

const mockData = [
  { name: "Melani Pham", email: "<EMAIL>", revenue: "$2,000", commission: "$1,000", avatar: "/avatars/melani.png" },
  { name: "Elio Mathews", email: "<EMAIL>", revenue: "$1,200", commission: "$800", avatar: "/avatars/elio.png" },
  { name: "Sasha Hale", email: "<EMAIL>", revenue: "$1,000", commission: "$600", avatar: "/avatars/sasha.png" },
  { name: "Mindi Schay", email: "<EMAIL>", revenue: "$800", commission: "$500", avatar: "/avatars/mindi.png" },
  { name: "Elisabeth Smith", email: "<EMAIL>", revenue: "$700", commission: "$300", avatar: "/avatars/elisabeth.png" },
];

export default function TopAgentTable() {
  const [query, setQuery] = useState({});
  const [rowSelection, setRowSelection] = useState({});

  return (
    <DataTable
      data={mockData || emptyData}
      columns={getColumns()}
      query={query}
      setQuery={setQuery}
      rowSelection={rowSelection}
      setRowSelection={setRowSelection}
      isRowSelection={false}
      uniqueField="email"
      numbered={true}
      paginationType="client-side"
      tableContainerClassName="rounded-md"
      isLoading={false}
    />
  );
}
