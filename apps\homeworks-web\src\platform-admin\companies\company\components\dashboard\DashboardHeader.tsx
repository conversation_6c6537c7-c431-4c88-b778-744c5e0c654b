import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@vast-application-framework/ui-components";
import { Download } from "lucide-react";
import { Button }from "@vast-application-framework/ui-components";

export default function DashboardHeader() {
  return (
    <div className="flex justify-between items-center mb-6">
      <h1 className="text-2xl font-semibold">Dashboard</h1>
      <div className="flex gap-2">
        {/* <Select defaultValue="June">
          <SelectTrigger className="w-32">
            <SelectValue placeholder="Select Month" />
          </SelectTrigger>
          <SelectContent>
            {["May", "June", "July"].map((m) => (
              <SelectItem key={m} value={m}>{m}</SelectItem>
            ))}
          </SelectContent>
        </Select> */}
        <Button variant="outline"><Download className="w-4 h-4 mr-2" /> Export</Button>
      </div>
    </div>
  );
}
