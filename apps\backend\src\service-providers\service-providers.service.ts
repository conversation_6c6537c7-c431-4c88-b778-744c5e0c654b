import {
  Injectable,
  BadRequestException,
  Logger,
  InternalServerErrorException,
} from '@nestjs/common';
import { CreateServiceProviderDto } from './dto/create-service-provider.dto';
import { ServiceProvidersRepository } from './repositories/service-providers.repository';
import {
  CodeGeneratorService,
  EmailService,
  ExcelService,
  HandleGeneratorService,
} from '@common';
import { MinioService } from '../common/minio/minio.service';
import { PageOptionsDto } from '../common/pagination/pageOptions.dto';
import { SortSearchDataDto } from '../common/pagination/sort-search-data.dto';
import { ServiceProviderFilterDto } from './dto/filter-service-provider.dto';
import {
  ActivateDeactivateServiceProviderDto,
  serviceProviderStatusDto,
  spStatusDto,
} from './dto/activate-deactivate-service-provider.dto';
import { Identifier } from '@shared-types';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Configuration } from '../configuration/entities/configuration.entity';
import { DeepPartial, Repository } from 'typeorm';
import { Response } from 'express';
import {
  ServiceProvidersForOwnerDto,
  ServiceProviderTypeEnum,
} from './dto/service-providers-for-owner.dto';
import { User } from '../users/entities/user.entity';
import { CartService } from '../cart/cart.service';
import { Cart } from '../cart/entities/cart.entity';
import { CartItem } from '../cart/entities/cart-item.entity';
import { UpdateServiceProviderDto } from './dto/update-service-provider.dto';
import { ServiceProvider } from './entities/service-provider.entity';
import { OwnerFavoriteServices } from '../favourite-service/entities/owner-favourite-services.entity';
import { Address } from '../users/entities/address.entity';
import { ZipCode } from '../zip-codes/entities/zip-code.entity';
import { StorageService } from '@storage';
import { BUCKET } from '../common/minio/buckets';
import { SERVICE_PROVIDER_DOCUMENT_TYPE_ENUM } from './entities/service-provider-document.entity';
import { SpDocumentObjectType } from './types';
import { getNewServiceProviderCategoryItems } from '../common/utils/utils';

@Injectable()
export class ServiceProvidersService {
  constructor(
    private readonly serviceProvidersRepository: ServiceProvidersRepository,
    private readonly codeGeneratorService: CodeGeneratorService,
    private readonly minioService: MinioService,
    private readonly trimmedNameGeneratorService: HandleGeneratorService,
    private readonly emailService: EmailService,
    private readonly configService: ConfigService,
    private readonly excelService: ExcelService,
    @InjectRepository(Configuration)
    private configRepository: Repository<Configuration>,
    private readonly logger: Logger,
    private readonly cartService: CartService,
    @InjectRepository(OwnerFavoriteServices)
    private ownerFavoriteServicesRepository: Repository<OwnerFavoriteServices>,
    private readonly storageService: StorageService,
  ) {}

  async createServiceProvider(
    createServiceProviderDto: CreateServiceProviderDto,
    files: {
      businessLicense?: Express.Multer.File[];
      ownerId?: Express.Multer.File[];
      insurance?: Express.Multer.File[];
    },
  ) {
    try {
      if (
        createServiceProviderDto.type &&
        createServiceProviderDto.type === 'linking'
      ) {
        const { serviceProviderWithRelations, loggedInUser } =
          await this.serviceProvidersRepository.createServiceProvider(
            createServiceProviderDto,
            null,
          );
        const serviceProviderManager =
          serviceProviderWithRelations?.serviceProviderUsers[0]?.userRole?.user;
        await this.sendLinkingRequestEmail(
          serviceProviderManager.email,
          serviceProviderWithRelations.name,
          `${serviceProviderManager?.firstName} ${serviceProviderManager?.lastName}`,
          loggedInUser?.firstName + ' ' + loggedInUser?.lastName,
          createServiceProviderDto.marketCenterId ? 'Market Center' : 'Company',
          createServiceProviderDto.marketCenterId
            ? serviceProviderWithRelations.marketCenterName
            : serviceProviderWithRelations?.accountName,
        );
        return {
          data: serviceProviderWithRelations,
          message:
            'Service provider linked with the market center successfully',
        };
      } else {
        const code = await this.codeGeneratorService.generateUniqueCode({
          type: 'alphanumeric',
          prefix: 'SP',
          length: 8,
          repository: this.serviceProvidersRepository,
        });

        const uploadedFiles = await this.uploadFiles(
          files,
          'service-providers',
        );

        const { trimmedName } =
          await this.trimmedNameGeneratorService.generateTrimmedNameAndHandle({
            name: createServiceProviderDto.name,
            repository: this.serviceProvidersRepository,
            entityName: 'Service Provider',
          });

        const { serviceProviderWithRelations, usersWithPlainPassword } =
          await this.serviceProvidersRepository.createServiceProvider(
            {
              ...createServiceProviderDto,
              name: trimmedName,
              code,
            },
            uploadedFiles,
          );

        const { firstName, lastName, contactNumber, email } =
          serviceProviderWithRelations?.createdByUser;

        if (usersWithPlainPassword?.length > 0) {
          await this.sendCredentialsEmailsInParallel(
            usersWithPlainPassword,
            serviceProviderWithRelations?.name,
            createServiceProviderDto.marketCenterId
              ? serviceProviderWithRelations.marketCenterName
              : serviceProviderWithRelations?.accountName,
            `${firstName} ${lastName}`,
            email,
            contactNumber,
            createServiceProviderDto.marketCenterId
              ? 'Market Center Manager'
              : 'Company Admin',
            createServiceProviderDto.marketCenterId
              ? 'Market Center'
              : 'Company',
          );
        }

        return {
          data: serviceProviderWithRelations,
          message: 'Service provider created successfully',
        };
      }
    } catch (error) {
      throw new BadRequestException(
        error?.message ? error?.message : 'Failed to create service provider',
      );
      throw new BadRequestException(
        error?.message ? error?.message : 'Failed to create service provider',
      );
    }
  }

  private async uploadFiles(
    files: {
      businessLicense?: Express.Multer.File[];
      ownerId?: Express.Multer.File[];
      insurance?: Express.Multer.File[];
    },
    bucketName: string,
  ) {
    const uploadedFiles = {
      businessLicense: [],
      ownerId: [],
      insurance: [],
    };

    if (files.businessLicense?.[0]) {
      uploadedFiles['businessLicense'] = await Promise.all(
        files.businessLicense.map((file) =>
          this.minioService.uploadFile(file, bucketName),
        ),
      );
    }

    if (files.ownerId?.[0]) {
      uploadedFiles['ownerId'] = await Promise.all(
        files.ownerId.map((file) =>
          this.minioService.uploadFile(file, bucketName),
        ),
      );
    }

    if (files.insurance?.[0]) {
      uploadedFiles['insurance'] = await Promise.all(
        files.insurance.map((file) =>
          this.minioService.uploadFile(file, bucketName),
        ),
      );
    }

    return uploadedFiles;
  }

  async findFilteredServiceProviders(
    pageOptionsDto: PageOptionsDto,
    sortSearchDataDto: SortSearchDataDto,
    filterServiceProviderDto: ServiceProviderFilterDto,
  ) {
    const needsInMemorySorting = sortSearchDataDto.sortColumn === 'name';
    const modifiedSortData = { ...sortSearchDataDto };
    if (needsInMemorySorting) {
      delete modifiedSortData.sortColumn;
    }

    // Fetch from repository
    const { serviceProviders, totalRecords, totalRecordsWithoutFilters } =
      await this.serviceProvidersRepository.findFilteredServiceProviders(
        needsInMemorySorting ? null : pageOptionsDto,
        modifiedSortData,
        filterServiceProviderDto,
      );
    // console.log(serviceProviders)
    let formattedServiceProviders = serviceProviders.map((sp) => ({
      id: sp.marketCenterAccounts[0]?.id,
      serviceProviderId: sp.id,
      code: sp.code,
      name: sp.name,
      isActive:
        !filterServiceProviderDto.marketCenterId &&
        !filterServiceProviderDto.accountId
          ? sp.isActive
          : sp.marketCenterAccounts[0]?.isActive,
      createdAt: sp.marketCenterAccounts[0]?.createdAt,
      categories:
        sp.serviceCategories?.map((cat) => ({
          id: cat.serviceCategory.id,
          name: cat.serviceCategory.name,
        })) || [],
      addedBy: {
        user: {
          firstName: sp.marketCenterAccounts[0]?.addedBy?.user?.firstName || '',
          lastName: sp.marketCenterAccounts[0]?.addedBy?.user?.lastName || '',
        },
        role: {
          name: sp.marketCenterAccounts[0]?.addedBy?.role?.name || '',
        },
        entity: {
          entity: sp.marketCenterAccounts[0].marketCenter?.name
            ? sp.marketCenterAccounts[0].marketCenter?.name
            : sp.marketCenterAccounts[0].account?.name
            ? sp.marketCenterAccounts[0]?.account?.name
            : '',
        },
      },
      status: {
        id: sp.marketCenterAccounts[0]?.status?.id || '',
        name: sp.marketCenterAccounts[0]?.status?.name || 'PENDING',
      },
      showToggle: !/pending|rejected/i.test(
        sp.marketCenterAccounts[0]?.status?.name || '',
      ),
      serviceAreas: sp.serviceAreas,
      address: sp.address,
      serviceProviderManager: sp.serviceProviderUsers,
      incorporationDate: sp.incorporationDate,
      email: sp.email,
      phoneNumber: sp.phoneNumber,
      serviceProviderDocuments: sp.serviceProviderDocuments?.reduce(
        (acc, doc) => {
          const key = doc.type?.toLowerCase(); // e.g. 'businesslicense'
          if (!acc[key]) acc[key] = [];
          acc[key].push(doc.document); // or push full `doc` if needed
          return acc;
        },
        {},
      ),
    }));

    // If sorting is required in-memory
    if (needsInMemorySorting) {
      const sortOrder = sortSearchDataDto.sortOrder?.toUpperCase() || 'ASC';
      const collator = new Intl.Collator(undefined, { sensitivity: 'base' });

      formattedServiceProviders.sort((a, b) =>
        sortOrder === 'ASC'
          ? collator.compare(a.name || '', b.name || '')
          : collator.compare(b.name || '', a.name || ''),
      );

      // Manual pagination
      if (pageOptionsDto && pageOptionsDto.paginate !== false) {
        const page = Number(pageOptionsDto.page) || 1;
        const limit = Number(pageOptionsDto.limit) || 10;
        const startIndex = (page - 1) * limit;
        formattedServiceProviders = formattedServiceProviders.slice(
          startIndex,
          startIndex + limit,
        );
      }
    }
    return {
      data: formattedServiceProviders,
      meta: {
        totalRecords,
        totalPages: Math.ceil(totalRecords / pageOptionsDto.limit),
        currentPage: pageOptionsDto.page,
        itemsPerPage: pageOptionsDto.limit,
        totalRecordsWithoutFilters,
      },
      message: 'Service providers fetched successfully',
    };
  }

  async activateDeactivateServiceProvider(
    id: Identifier,
    activateDeactivateServiceProviderDto: ActivateDeactivateServiceProviderDto,
    accountId?: string,
    marketCenterId?: string,
  ) {
    const updatedServiceProvider =
      await this.serviceProvidersRepository.activateDeactivateServiceProvider(
        id,
        activateDeactivateServiceProviderDto.isActive,
        accountId,
        marketCenterId,
      );
    return {
      data: updatedServiceProvider,
      message: `Service provider ${
        activateDeactivateServiceProviderDto.isActive
          ? 'activated'
          : 'deactivated'
      } successfully`,
    };
  }

  async softDeleteServiceProvider(id: Identifier) {
    const deletedServiceProvider =
      await this.serviceProvidersRepository.softDeleteServiceProvider(id);
    return {
      data: deletedServiceProvider,
      message: 'Service provider deleted successfully',
    };
  }

  async sendCredentialsEmailsInParallel(
    users: Array<{
      firstName: string;
      lastName: string;
      username: string;
      email: string;
      plainPassword: string;
    }>,
    serviceProviderName: string,
    senderEntityName: string,
    senderName: string,
    senderEmail: string,
    senderPhone: string,
    senderRole: string,
    senderEntity: string,
  ): Promise<void> {
    const batchSize = 5;
    const eligibleUsers = users.filter((u) => u.plainPassword);
    for (let i = 0; i < eligibleUsers.length; i += batchSize) {
      const batch = eligibleUsers.slice(i, i + batchSize);
      const emailPromises = batch.map((user) =>
        this.sendCredentialsEmail({
          email: user.email,
          password: user.plainPassword,
          serviceProviderName,
          serviceProviderManagerName: `${user.firstName} ${user.lastName}`,
          senderEntityName,
          senderName,
          senderEmail,
          senderPhone,
          senderRole,
          senderEntity,
        }),
      );

      try {
        await Promise.all(emailPromises);
      } catch (error) {
        //continue with next batch
        this.logger.error('error', error);
      }
    }
  }

  private async sendCredentialsEmail({
    email,
    password,
    senderEntityName,
    senderName,
    serviceProviderName,
    serviceProviderManagerName,
    senderEmail,
    senderPhone,
    senderRole,
    senderEntity,
  }: {
    email: string;
    password: string;
    senderName: string;
    serviceProviderName: string;
    serviceProviderManagerName: string;
    senderEmail: string;
    senderPhone: string;
    senderRole: string;
    senderEntity: string;
    senderEntityName: string;
  }): Promise<void> {
    const loginUrl =
      this.configService.get<string>('SERVICE_PROVIDER_PLAYSTORE_APP_URL') ??
      '';

    try {
      const emailData = await this.emailService.generateEmail({
        to: email,
        subject: `Welcome to HomeWorks - Your Mobile App Login Details`,
        templateName: 'SERVICE_PROVIDER_MANAGER_CREDENTIALS',
        templateData: {
          emailLogo: this.configService.get<string>('EMAIL_LOGO'),
          emailImage: this.configService.get<string>('EMAIL_IMAGE'),
          serviceProviderName,
          serviceProviderManagerName,
          senderEntityName,
          senderName,
          senderEmail,
          senderPhone,
          email,
          password,
          loginUrl,
          senderRole,
          senderEntity,
          body: `Welcome to <strong>HomeWorks</strong>! <br><br>
            We are glad to have your service team on board <br><br>
            You have been registered as a <strong>Service Provider Manager</strong> for ${serviceProviderName} on the HomeWorks mobile app. 
            Please download the app using the link below and log in using the credentials provided below. <br><br/>`,
        },
        configRepository: this.configRepository,
      });
      await this.emailService.sendEmail(emailData);
    } catch (error) {
      this.logger.error('error', error);
    }
  }

  async findServiceProviderById(id: string) {
    const serviceProvider =
      await this.serviceProvidersRepository.findOneServiceProviderBuUniqueField(
        { id },
      );
    const serviceProviderDocuments = serviceProvider?.serviceProviderDocuments;
    serviceProvider['serviceProviderDocuments'] =
      await this.appendUrlToDocumentsAndSegregateByType(
        serviceProviderDocuments,
      );
    return serviceProvider;
  }

  async findServiceProviderByCode(
    code: string,
    currentUser: any,
    accountId?: any,
    marketCenterId?: any,
    viewOnly?: boolean,
  ) {
    const serviceProvider =
      await this.serviceProvidersRepository.findOneServiceProviderBuUniqueField(
        { code, currentUser, accountId, marketCenterId, viewOnly },
      );
    const serviceProviderDocuments = serviceProvider?.serviceProviderDocuments;
    serviceProvider['serviceProviderDocuments'] =
      await this.appendUrlToDocumentsAndSegregateByType(
        serviceProviderDocuments,
      );

    return serviceProvider;
  }

  private async appendUrlToDocumentsAndSegregateByType(
    serviceProviderDocuments,
  ) {
    if (serviceProviderDocuments?.length > 0) {
      for (let i = 0; i < serviceProviderDocuments.length; i++) {
        serviceProviderDocuments[i].document['url'] =
          await this.minioService.getFileUrl(
            serviceProviderDocuments[i].document.minioFilename,
            'service-providers',
            false,
          );
        serviceProviderDocuments[i].document['download'] =
          await this.minioService.getFileUrl(
            serviceProviderDocuments[i].document.minioFilename,
            'service-providers',
            true,
          );
        serviceProviderDocuments[i].document['fileSize'] =
          await this.minioService.getFileSize(
            serviceProviderDocuments[i].document.minioFilename,
            'service-providers',
          );
      }
    }
    const segregatedServiceProviderDocuments = {
      businessLicense: serviceProviderDocuments
        .filter((spd) => spd.type === 'BUSINESS_LICENSE')
        .map((tspd) => tspd.document),
      ownerId: serviceProviderDocuments
        .filter((spd) => spd.type === 'OWNER_ID')
        .map((tspd) => tspd.document),
      insurance: serviceProviderDocuments
        .filter((spd) => spd.type === 'INSURANCE')
        .map((tspd) => tspd.document),
    };
    return segregatedServiceProviderDocuments as any;
  }

  async exportServiceProvidersToExcel(
    sortSearchDataDto: SortSearchDataDto,
    filterServiceProviderDto: ServiceProviderFilterDto,
    response: Response,
  ) {
    const { serviceProviders } =
      await this.serviceProvidersRepository.findFilteredServiceProviders(
        null,
        sortSearchDataDto,
        {
          ...filterServiceProviderDto,
          ids: filterServiceProviderDto.ids
            ? filterServiceProviderDto.ids
            : null,
        },
      );

    if (!serviceProviders.length) {
      throw new BadRequestException('No data available to export');
    }
    const formattedServiceProviders = serviceProviders.map(
      (sp, index: number) => {
        let spManager = sp.serviceProviderUsers?.find(
          (spu) => spu.userRole?.role?.name === 'service_provider_manager',
        )?.userRole?.user;
        let statusToShow =
          filterServiceProviderDto.userRole !== 'platform_admin' &&
          sp.marketCenterAccounts[0]?.status?.name &&
          sp.marketCenterAccounts[0]?.status?.name ===
            'PENDING_APPROVAL_BY_SERVICE_PROVIDER_MANAGER'
            ? 'Pending from SP'
            : filterServiceProviderDto.userRole !== 'platform_admin' &&
              sp.marketCenterAccounts[0]?.status?.name ===
                'PENDING_APPROVAL_BY_MARKET_CENTER_MANAGER'
            ? 'Pending'
            : filterServiceProviderDto.userRole !== 'platform_admin' &&
              sp.marketCenterAccounts[0]?.status?.name ===
                'REJECTED_BY_MARKET_CENTER_MANAGER'
            ? 'Rejected by MCM'
            : filterServiceProviderDto.userRole !== 'platform_admin' &&
              sp.marketCenterAccounts[0]?.status?.name ===
                'REJECTED_BY_SERVICE_PROVIDER_MANAGER'
            ? 'Rejected by SP'
            : filterServiceProviderDto.userRole == 'platform_admin' &&
              sp?.marketCenterAccounts[0]?.isActive
            ? 'Active'
            : filterServiceProviderDto.userRole == 'platform_admin' &&
              !sp.isActive
            ? 'Inactive'
            : 'Active';
        if (sp?.marketCenterAccounts[0]?.status?.name === 'APPROVED') {
          if (filterServiceProviderDto.userRole == 'platform_admin') {
            statusToShow = sp.isActive ? 'Active' : 'Inactive';
          }
          return {
            'Sr. No': index + 1,
            'Service Provider ID': sp.code,
            'Service Provider Name': sp.name,
            'Service Provider Contact No.': sp.phoneNumber,
            Categories:
              sp.serviceCategories
                ?.map((cat) => cat.serviceCategory.name)
                .join(', ') || '',
            'SP Manager Name':
              spManager?.firstName + ' ' + spManager?.lastName || '',
            'SP Manager Contact No.': spManager?.contactNumber || '',
            Address:
              sp.address?.street +
              ', ' +
              sp.address?.city +
              ', ' +
              sp.address?.state +
              ', ' +
              sp.address?.zipcode,
            'Serviceable Zipcodes':
              sp.serviceAreas
                ?.map(
                  (sa) =>
                    `${sa?.zipCode} - ${sa?.city?.cityName},${sa?.state?.stateName}`,
                )
                ?.join('\r\n') || '',
            'Linked Agents': '0',
            // 'Added By': sp.marketCenterAccounts[0]?.addedBy?.user?.firstName + ' ' + sp.marketCenterAccounts[0]?.addedBy?.user?.lastName  +  "(" + sp.marketCenterAccounts[0]?.addedBy?.role.name +")",
            // 'Added By':  sp.marketCenterAccounts[0].marketCenter.name +  "(" + sp.marketCenterAccounts[0]?.addedBy?.role.name + ": "+ sp.marketCenterAccounts[0]?.addedBy?.user?.firstName + ' ' + sp.marketCenterAccounts[0]?.addedBy?.user?.lastName  +")",
            'Added By': `${
              sp.marketCenterAccounts[0]?.account?.name ||
              sp.marketCenterAccounts[0]?.marketCenter?.name ||
              'Provision Manager'
            } (${sp.marketCenterAccounts[0]?.addedBy?.role?.name || ''}: ${
              sp.marketCenterAccounts[0]?.addedBy?.user?.firstName || ''
            } ${sp.marketCenterAccounts[0]?.addedBy?.user?.lastName || ''})`,

            'Added on Date':
              sp.marketCenterAccounts[0]?.createdAt?.toLocaleDateString() || '',
            Status: statusToShow,
          };
        }
      },
    );

    await this.excelService.generateExcelSheet({
      data: formattedServiceProviders,
      response,
      fileName: 'Service_Provider_List',
    });
  }

  private async sendLinkingRequestEmail(
    email: string,
    serviceProviderName: string,
    serviceProviderManagerName: string,
    requestedByUserFirstNameLastName: string,
    entity: string,
    entityName: string,
  ): Promise<void> {
    try {
      const emailData = await this.emailService.generateEmail({
        to: email,
        subject: `Action Needed: Approve or Reject Market Center Association Request`,
        templateName: 'SERVICE_PROVIDER_MANAGER_LINKING_REQUEST',
        templateData: {
          emailLogo: this.configService.get<string>('EMAIL_LOGO'),
          emailImage: this.configService.get<string>('EMAIL_IMAGE'),
          serviceProviderName,
          serviceProviderManagerName,
          requestedByUserFirstNameLastName,
          entity,
          email,
          approveUrl:
            this.configService.get<string>(
              'SERVICE_PROVIDER_PLAYSTORE_APP_URL',
            ) ?? '',
          rejectUrl:
            this.configService.get<string>(
              'SERVICE_PROVIDER_PLAYSTORE_APP_URL',
            ) ?? '',
          entityName,
        },
        configRepository: this.configRepository,
      });
      await this.emailService.sendEmail(emailData);
    } catch (error) {
      this.logger.error('error', error);
    }
  }

  async findAllServiceProvidersForOwner(
    query: ServiceProvidersForOwnerDto,
    user: User,
  ) {
    const response =
      await this.serviceProvidersRepository.findAllServiceProvidersForOwner(
        query,
        user,
      );

    const data = response?.reduce(
      (res, current) => {
        if (res.seenIds.has(current.id)) return res; // Skip duplicates

        res.seenIds.add(current.id);
        if (current.type === ServiceProviderTypeEnum.recommended) {
          res.recommended.data.push(current);
          res.recommended.total += 1;
          if (!res?.recommended?.agent?.name) {
            res.recommended.agent.name = current.userName;
            res.recommended.agent.phoneNumber = current.userContactNumber;
          }
        } else {
          res.others.data.push(current);
          res.others.total += 1;
        }
        return res;
      },
      {
        seenIds: new Set<string>(),
        recommended: {
          data: [],
          total: 0,
          agent: {
            name: null,
            phoneNumber: null,
          },
        },
        others: {
          data: [],
          total: 0,
        },
      },
    );

    return data;
  }

  async findServiceProviderByIdForOwner(
    id: string,
    query: Pick<ServiceProviderFilterDto, 'categoryId' | 'propertyId'>,
    ownerId: string,
  ) {
    const serviceProvider =
      await this.serviceProvidersRepository.findOneServiceProviderForOwnerByUniqueField(
        { id },
        query,
      );

    const cart = await this.cartService.findOneByQuery({
      ownerId,
      propertyId: query.propertyId,
      serviceProviderId: id,
    });

    const cartWithDiffServiceProvider = await this.cartService.findOneByQuery({
      ownerId,
      propertyId: query.propertyId,
      serviceProviderId: id,
      serviceProviderNotIn: true,
    });

    const { cartItems, ...cartData } = cart ?? {};

    const map = new Map<string, CartItem & { cartData: Partial<Cart> }>();
    serviceProvider['isCartFilled'] = false;
    if (cartWithDiffServiceProvider?.id) {
      serviceProvider['isCartFilled'] = true;
    }

    if (cartItems?.length) {
      cartItems?.forEach((cartItem) => {
        map.set(`service_${cartItem.serviceProviderService.id}`, {
          ...cartItem,
          cartData,
        });
        map.set(`service_variant_${cartItem.serviceProviderVariant.id}`, {
          ...cartItem,
          cartData,
        });
      });
    }

    if (cartItems?.length) {
      cartItems?.forEach((cartItem) => {
        map.set(`service_${cartItem.serviceProviderService.id}`, {
          ...cartItem,
          cartData,
        });
        map.set(`service_variant_${cartItem.serviceProviderVariant.id}`, {
          ...cartItem,
          cartData,
        });
      });
    }

    const favoriteServiceRecords =
      await this.ownerFavoriteServicesRepository.find({
        where: { userRoleId: { id: ownerId } },
        relations: ['service'],
      });

    const favoriteServiceIds = new Set(
      favoriteServiceRecords.map((fav) => fav.service.id),
    );

    serviceProvider.serviceCategories = serviceProvider.serviceCategories.map(
      (serviceCategory) => {
        return {
          ...serviceCategory,
          serviceProviderServices: serviceCategory.serviceProviderServices.map(
            (serviceProviderService) => {
              const variants = serviceProviderService.variants;
              delete serviceProviderService.variants;
              let cartVariant = null;
              if (map.has(`service_${serviceProviderService.id}`)) {
                const cartItem = map.get(
                  `service_${serviceProviderService.id}`,
                );
                cartVariant = {
                  cartId: cartItem?.cartData?.id,
                  cartItemId: cartItem?.id,
                  serviceProviderServiceId: cartItem.serviceProviderService.id,
                  serviceProviderServiceVariantId:
                    cartItem.serviceProviderVariant.id,
                  cartItemPrice: cartItem.amount,
                };
              }

              let startPrice = 0;
              let currency = null;
              if (variants?.length) {
                startPrice = variants.reduce(
                  (minPrice, { finalPrice, currency: { symbol } }) => {
                    currency = symbol;
                    return +finalPrice <= minPrice ? finalPrice : minPrice;
                  },
                  +variants[0].finalPrice,
                );
              }
              return {
                ...serviceProviderService,
                totalVariants: variants?.length ?? 0,
                startPrice: +startPrice,
                currency,
                cartVariant,
                isFavourite: favoriteServiceIds.has(
                  serviceProviderService.service.id,
                ),
              };
            },
          ),
        };
      },
    );
    return serviceProvider;
  }

  async updateServiceProvider(
    id: string,
    serviceProviderStatusDto: serviceProviderStatusDto,
  ) {
    return this.serviceProvidersRepository.updateStatus(
      id,
      serviceProviderStatusDto,
    );
  }

  async getBusinessDetailsServiceProviderById(id: string, userId: string) {
    const exisitingServiceProvider =
      await this.serviceProvidersRepository.findOne({
        where: {
          id,
          isActive: true,
          isDeleted: false,
          serviceProviderUsers: {
            userRole: {
              userId,
            },
          },
          serviceProviderDocuments: {
            isActive: true,
            isDeleted: false,
            serviceProvider: {
              id,
            },
            document: {
              isActive: true,
              isDeleted: false,
            },
          },
        },
        relations: {
          address: true,
          serviceCategories: {
            serviceCategory: true,
          },
          serviceProviderUsers: {
            userRole: {
              user: true,
            },
          },
          serviceAreas: true,
          serviceProviderDocuments: {
            document: true,
          },
        },
      });

    if (!exisitingServiceProvider?.id) {
      this.logger.error(
        `Business details (service provider) with id: ${id} not found`,
      );
      throw new BadRequestException('Business details with id not found');
    }

    const {
      name,
      address,
      imageUrl,
      serviceCategories,
      email,
      phoneNumber,
      incorporationDate,
      description,
      businessLicenseExpiryDate,
      serviceProviderUsers,
      serviceAreas,
      serviceProviderDocuments,
    } = exisitingServiceProvider;

    if (serviceProviderDocuments?.length) {
      await Promise.all(
        serviceProviderDocuments?.map(async (spDoc) => {
          const url = await this.minioService.getFileUrl(
            spDoc.document.minioFilename,
            BUCKET.HOMEWORKS.FOLDERS.SERVICE_PROVIDER,
          );
          spDoc['url'] = url;
        }),
      );
    }

    const businessDetails = {
      id: exisitingServiceProvider.id,
      name,
      email,
      imageUrl,
      phoneNumber,
      incorporationDate,
      description,
      businessLicenseExpiryDate,
      address,
      serviceCategories: serviceCategories?.map((sc) => ({
        id: sc?.id,
        serviceCategory: {
          id: sc?.serviceCategory?.id,
          name: sc?.serviceCategory?.name,
          code: sc?.serviceCategory?.code,
          handle: sc?.serviceCategory?.handle,
          imageUrl: sc?.serviceCategory?.imageUrl,
          isDeleted: sc?.serviceCategory?.isDeleted,
        },
      })),
      businessManager: serviceProviderUsers?.at(0)?.userRole?.user || null,
      serviceAreas: serviceAreas?.map((sa) => ({
        id: sa?.id,
        zipCode: sa?.zipCode,
      })),
      serviceProviderDocuments,
    };

    return {
      data: businessDetails,
      message: 'Business details fetched successfully',
    };
  }
  async updateServiceProviderDetails(
    id: string,
    userId: string,
    files: {
      profilePhoto?: Express.Multer.File[];
      ownerId?: Express.Multer.File[];
      businessLicense?: Express.Multer.File[];
      insurance?: Express.Multer.File[];
    },
    updateServiceProviderDto: UpdateServiceProviderDto,
  ) {
    const existingServiceProvider = await this.findServiceProviderById(id);

    if (!existingServiceProvider) {
      this.logger.error(`Service provider not found with id:${id}`);
      throw new BadRequestException('Service provider not found');
    }

    const {
      businessAddress,
      businessContact,
      businessDetails,
      businessManager,
      serviceInformation,
    } = updateServiceProviderDto;

    const businessDetailsDto: DeepPartial<ServiceProvider> = {};

    if (businessContact?.email) {
      businessDetailsDto.email = businessContact?.email;
    }
    if (businessContact?.incorporationDate) {
      businessDetailsDto.incorporationDate = businessContact?.incorporationDate;
    }
    if (businessContact?.phoneNumber) {
      businessDetailsDto.phoneNumber = businessContact?.phoneNumber;
    }
    if (businessDetails?.name) {
      businessDetailsDto.name = businessDetails?.name;
    }
    if (serviceInformation?.description) {
      businessDetailsDto.description = serviceInformation?.description;
    }

    if (serviceInformation?.businessLicenseExpiryDate) {
      businessDetailsDto.businessLicenseExpiryDate =
        serviceInformation?.businessLicenseExpiryDate;
    }

    let newServiceCategoryIds = [];
    if (businessDetails?.serviceCategoryIds?.length) {
      const exisitingCategories =
        existingServiceProvider?.serviceCategories?.map(
          (spCategory) => spCategory?.serviceCategory?.id,
        ) ?? [];
      newServiceCategoryIds = getNewServiceProviderCategoryItems(
        exisitingCategories,
        businessDetails?.serviceCategoryIds,
      );
    }

    const businessManagerDto: DeepPartial<User> = {};
    if (businessManager?.email) {
      businessManagerDto.email = businessManager?.email;
    }
    if (businessManager?.firstName) {
      businessManagerDto.firstName = businessManager?.firstName;
    }
    if (businessManager?.lastName) {
      businessManagerDto.lastName = businessManager?.lastName;
    }
    if (businessManager?.phoneNumber) {
      businessManagerDto.contactNumber = businessManager?.phoneNumber;
    }

    if (serviceInformation?.serviceableZipCodeIds?.length) {
      const zipCodes: DeepPartial<ZipCode[]> =
        serviceInformation?.serviceableZipCodeIds?.map((zip) => ({
          id: zip,
        }));

      // assuming that serviceableZipCodeIds are in the ZipCode table
      businessDetailsDto.serviceAreas = zipCodes;
    }

    let addressDto: DeepPartial<Address> = {};
    if (existingServiceProvider?.address?.id) {
      // address is already there need to update

      addressDto = {
        ...existingServiceProvider?.address,
        ...businessAddress,
      };
    }

    if (files?.profilePhoto?.length) {
      const profile = await this.storageService.uploadFile(
        files?.profilePhoto?.at(0),
      );
      businessDetailsDto.imageUrl = profile?.url;
    }

    const docs = await this.handleUploadFilesForBusinessDetails(
      files,
      existingServiceProvider,
    );

    const updatedBusinessDetails =
      await this.serviceProvidersRepository.updateServiceProviderDetails(
        id,
        userId,
        businessDetailsDto,
        businessManagerDto,
        addressDto,
        docs,
        newServiceCategoryIds,
      );

    if (!updatedBusinessDetails?.affected) {
      throw new InternalServerErrorException(
        "Something went wrong! Couldn't update business details!",
      );
    }
    return { data: { id }, message: 'Business details updated successfully' };
  }

  async handleUploadFilesForBusinessDetails(
    files: {
      profilePhoto?: Express.Multer.File[];
      ownerId?: Express.Multer.File[];
      businessLicense?: Express.Multer.File[];
      insurance?: Express.Multer.File[];
    },
    existingServiceProvider,
  ): Promise<SpDocumentObjectType> {
    let documentUploads = {
      update: [],
      new: [],
    };

    if (files?.businessLicense?.length) {
      const uploadedLicenseDoc = await this.minioService.uploadFile(
        files?.businessLicense?.at(0),
        BUCKET.HOMEWORKS.FOLDERS.SERVICE_PROVIDER,
      );

      const businessLicense =
        existingServiceProvider?.serviceProviderDocuments?.[
          'businessLicense'
        ]?.[0];

      if (businessLicense?.id) {
        documentUploads.update.push({
          ...uploadedLicenseDoc,
          documentId: businessLicense?.id,
          spdType: SERVICE_PROVIDER_DOCUMENT_TYPE_ENUM.BUSINESS_LICENSE,
        });
      } else {
        documentUploads.new.push({
          spdType: SERVICE_PROVIDER_DOCUMENT_TYPE_ENUM.BUSINESS_LICENSE,
          ...uploadedLicenseDoc,
        });
      }
    }

    if (files?.insurance?.length) {
      const uploadedInsuranceDoc = await this.minioService.uploadFile(
        files?.insurance?.at(0),
        BUCKET.HOMEWORKS.FOLDERS.SERVICE_PROVIDER,
      );

      const insurance =
        existingServiceProvider?.serviceProviderDocuments?.['insurance']?.[0];

      if (insurance?.id) {
        documentUploads.update.push({
          ...uploadedInsuranceDoc,
          documentId: insurance?.id,
          spdType: SERVICE_PROVIDER_DOCUMENT_TYPE_ENUM.INSURANCE,
        });
      } else {
        documentUploads.new.push({
          spdType: SERVICE_PROVIDER_DOCUMENT_TYPE_ENUM.INSURANCE,
          ...uploadedInsuranceDoc,
        });
      }
    }

    if (files?.ownerId?.length) {
      const uploadedOwnerIdDoc = await this.minioService.uploadFile(
        files?.ownerId?.at(0),
        BUCKET.HOMEWORKS.FOLDERS.SERVICE_PROVIDER,
      );

      const ownerId =
        existingServiceProvider?.serviceProviderDocuments?.['ownerId']?.[0];

      if (ownerId?.id) {
        documentUploads.update.push({
          ...uploadedOwnerIdDoc,
          documentId: ownerId?.id,
          spdType: SERVICE_PROVIDER_DOCUMENT_TYPE_ENUM.OWNER_ID,
        });
      } else {
        documentUploads.new.push({
          spdType: SERVICE_PROVIDER_DOCUMENT_TYPE_ENUM.OWNER_ID,
          ...uploadedOwnerIdDoc,
        });
      }
    }

    return documentUploads;
  }

  async updateServiceProviderFromWeb(
    id: string,
    spStatusFromWebDto: spStatusDto,
  ) {
    return this.serviceProvidersRepository.updateStatusFromWeb(id, spStatusFromWebDto);
  }
}
