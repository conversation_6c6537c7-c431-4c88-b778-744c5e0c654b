import { <PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';

type DonutData = { name: string; value: number; color: string };

export default function Donut<PERSON>hartCard({ data }: { data: DonutData[] }) {
  if (!data || data.length === 0) return <div>No data</div>;

  const total = data.reduce((acc, item) => acc + item.value, 0);

  return (
    <div className="flex items-center gap-4 bg-[white] ">
      <PieChart width={120} height={120}>
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          innerRadius={40}
          outerRadius={55}
          dataKey="value"
          stroke="none"
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={entry.color} />
          ))}
        </Pie>
      </PieChart>
      <ul className="text-sm">
        {data.map((entry) => (
          <li key={entry.name} className="flex justify-between gap-2 mb-1">
            <div className="flex items-center gap-2">
              <span
                className="block w-3 h-3 rounded-full"
                style={{ background: entry.color }}
              />
              <span>{entry.name}</span>
            </div>
            <span className="font-semibold">{entry.value}</span>
          </li>
        ))}
      </ul>
    </div>
  );
}
