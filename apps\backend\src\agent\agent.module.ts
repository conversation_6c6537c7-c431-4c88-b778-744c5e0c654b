import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AgentDetail } from './entities/agent.entity';
import { AgentRepository } from './repositories/agent.repository';
import { AgentService } from './agent.service';
import { Agent<PERSON>ontroller } from './agent.controller';
import { CodeGeneratorService, CommonModule, ExcelService } from '@common';
import { GovDocumentType } from './entities/gov-document-type.entity';
import { AddressProofDocumentType } from './entities/address_proof_document_type.entity';
import { AgentStatus } from './entities/agent-status.entity';
import { Document } from '../document/entities/document.entity';
import { User } from '../users/entities/user.entity';
import { State } from '../zip-codes/entities/state.entity';
import { Address } from '../users/entities/address.entity';
import { MarketCenter } from '../market-centers/entities/market-center.entity';
import { Account } from '../accounts/entities/account.entity';
import { AuditlogsModule } from '../auditlogs/auditlogs.module';
import { Role } from '../role/entities/role.entity';
import { UserRole } from '../role/entities/user-role.entity';
import { MinioModule } from '../common/minio/minio.module';
import { CommonService } from '../common/utils/common.service';
import { EmailService } from '../common/email/email.service';
import { SendgridService } from '../common/sendgrid/sendgrid.service';
import { ConfigService } from '@nestjs/config';
import { ConfigurationService } from '../configuration/configuration.service';
import { Configuration } from '../configuration/entities/configuration.entity';
import { ConfigurationRepository } from '../configuration/repositories/configuration.repository';
import { ConfigurationModule } from '../configuration/configuration.module';
import { UsersModule } from '../users/users.module';
import { Agent } from 'node:http';
import { AgentExperienceOption } from './entities/agent_experience_option.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AgentDetail,
      GovDocumentType,
      AddressProofDocumentType,
      AgentStatus,
      Document,
      User,
      State,
      Address,
      MarketCenter,
      Account,
      Role,
      UserRole,
      Configuration,
      AgentExperienceOption,
    ]),
    AuditlogsModule,
    MinioModule,
    CommonModule,
    ConfigurationModule,
    UsersModule,
  ],
  controllers: [AgentController],
  providers: [
    AgentRepository,
    AgentService,
    CodeGeneratorService,
    Logger,
    CommonService,
    EmailService,
    SendgridService,
    ConfigService,
    ConfigurationService,
    ConfigurationRepository,
    ExcelService,
  ],
  exports: [AgentRepository, AgentService],
})
export class AgentModule {}
