// import { Card, CardContent } from "@vast-application-framework/ui-components"

import Card from '../../../../../../src/components/card'

export const SummaryCard = ({
  title,
  value,
  icon,
  amountClass,
  containerClass
}: {
  title: string;
  value: string;
  icon?: React.ReactNode;
  amountClass? :string,
  containerClass? : string
}) => (
 <Card
          icon={icon}
          amount={value}
          amountClassName={amountClass}
          containerClassName={containerClass}
          text=""
          textClassName=""
          textColor=""
          title={title}
          iconClassName="px-0.5 py-1"
          titleClassName="text-[13px]"
        />
);
