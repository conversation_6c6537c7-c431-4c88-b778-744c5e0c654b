import {
  BadRequestException,
  ConflictException,
  HttpException,
  Injectable,
  Logger,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository, Not } from 'typeorm';
import { AccountDetail } from './entities/account-detail.entity';
import { Address } from '../users/entities/address.entity';
import { AccountsService } from '../accounts/accounts.service';
import { CompanyRepository } from './repositories/company.repository';
import { User } from '../users/entities/user.entity';
import { UserRepository } from '../users/repositories/user.repository';
import { PageDto } from '../common/pagination/page.dto';
import { SortSearchDataDto } from '../common/pagination/sort-search-data.dto';
import { PageOptionsDto } from '../common/pagination/pageOptions.dto';
import { CompanyFilterDto } from './dto/company-filter.dto';
import { PageMetaDto } from '../common/pagination/pageMeta.dto';
import { CompanyType } from './entities/company_type.entity';
import { StorageService } from '@storage';
import { CommonService } from '../common/utils/common.service';
import { EmailService } from '../common/email/email.service';
import { UsersService } from '../users/users.service';
import { DynamicValuesType } from '../accounts/types/types';
import { AccountTypes, EntityValue } from '@shared-types';
import { ExcelService } from '@common';
import { Response } from 'express';
import { AccountsRepository } from '../accounts/repositories/accounts.repository';
import { AccountUserRepository } from '../accountusers/repositories/account.user.repository';
import { UserRoleRepository } from '../role/repositories/user.role.repository';
import { UserRoleService } from '../role/user.role.service';
import { RoleRepository } from '../role/repositories/role.repository';
import { ConfigService } from '@nestjs/config';
import { UserRole } from '../role/entities/user-role.entity';
import { AccountUser } from '../accountusers/entities/account.user.entity';
import { Account } from '../accounts/entities/account.entity';
import { Role } from '../role/entities/role.entity';
import { AccountLegalDocument } from '../document/entities/account-legaldocument.entity';
import { Document } from '../document/entities/document.entity';

import { MinioService } from '../common/minio/minio.service';
// import { uploadImageToMinio } from '../common/utils/uploadMinio';
// import { max_image_size } from '../common/constants';
@Injectable()
export class CompanyService extends Repository<AccountDetail> {
  constructor(
    private readonly companyRepository: CompanyRepository,
    private readonly accountsService: AccountsService,
    @InjectRepository(Address)
    private readonly addressRepository: Repository<Address>,
    private readonly userRepository: UserRepository,
    private readonly datasource: DataSource,
    @InjectRepository(CompanyType)
    private readonly companyTypeRepository: Repository<CompanyType>,
    private readonly storageService: StorageService,
    private readonly commonService: CommonService,
    private readonly emailService: EmailService,
    private readonly usersService: UsersService,
    private readonly logger: Logger,
    private readonly excelService: ExcelService,
    private readonly accountRepository: AccountsRepository,
    private readonly accountUserRepository: AccountUserRepository,
    private readonly userRoleRepository: UserRoleRepository,
    private readonly roleRepository: RoleRepository,
    private readonly configService: ConfigService,
    private readonly minioService: MinioService,
    private readonly userRoleService: UserRoleService,
  ) {
    super(AccountDetail, datasource.createEntityManager());
  }

  async generateUniqueCompanyId(): Promise<string> {
    let isUnique = false;
    let newCompanyId: string;

    while (!isUnique) {
      const randomNum = Math.floor(100000 + Math.random() * 900000);
      newCompanyId = `HC${randomNum}`;

      const existingCompany =
        await this.accountRepository.checkIfAccountCodeExists(newCompanyId);

      if (!existingCompany) {
        isUnique = true;
      }
    }

    return newCompanyId;
  }
  async sendPasswordByEmail(
    toEmail: string,
    accountName?: string,
    logoImage?: string,
    password?: string,
    firstName?: string,
    lastName?: string,
  ): Promise<void> {
    try {
      const initials: string = accountName
        ? accountName.substring(0, 2).toUpperCase()
        : '';
      const invitationURL = `${this.configService.get<string>('APP_URL')}/`;
      const subject = ` Welcome to HomeWorks – Your Admin Account is Ready!`;
      const logoOrInitials: string = logoImage
        ? `<mj-image src="${logoImage}" width="80px" height="80px"/>`
        : `<mj-text font-size="36px" color="#504EBE" font-weight="bold" text-align="center" padding="20px" align="center">${initials}</mj-text>`;

      const dynamicValues: DynamicValuesType = {
        logoImage: logoImage || null,
        header: accountName ?? '',
        password: password,
        logoOrInitials,
        loginUrl: invitationURL,
        email: toEmail,
        companyName: accountName ?? '',
        companyAdminName: firstName
          ? lastName
            ? `${firstName} ${lastName}`
            : firstName
          : lastName
          ? lastName
          : 'User',
      };

      // Generate and send the email
      await this.emailService.generateAndSendEmail({
        to: toEmail,
        subject: subject,
        templateName: 'COMPANY_ADMIN_PASSWORD',
        templateData: dynamicValues as EntityValue,
      });
      this.logger.log(`Password sent successfully to ${toEmail}`);
    } catch (error) {
      this.logger.error(`Error while sending Password to ${toEmail}`, error);
      throw new Error(`Failed to send Password to ${toEmail}`);
    }
  }

  async uploadMultipleFiles(
    files: Express.Multer.File[],
    bucketName: string,
  ): Promise<any> {
    if (!files || files.length === 0) {
      return [];
    }

    try {
      const uploadPromises = files.map((file) =>
        this.minioService.uploadFile(file, bucketName),
      );

      const fileNames = await Promise.all(uploadPromises);
      return fileNames;
    } catch (error) {
      this.logger.error('Error uploading multiple files:', error);
      throw new BadRequestException(`Error uploading files: ${error.message}`);
    }
  }

  async createCompany(
    companyData: any,
    user: any,
    files?: Express.Multer.File[],
  ): Promise<{ data: any; message: string }> {
    const queryRunner = this.datasource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const companyName = companyData.company.name;
      const type = companyData.company.type;
      let companyTypeForCompany: any;
      if (!companyName || !type) {
        throw new NotAcceptableException(
          'Company name and company type is required.',
        );
      }

      let uploadedFileUrls;
      if (files && files.length > 0) {
        // Upload files to MinIO
        const fileNames = await this.uploadMultipleFiles(
          files,
          process.env.MINIO_DEFAULT_BUCKETS,
        );
        const filesWithUrls = await Promise.all(
          fileNames.map(async (file) => {
            const presignedUrl = await this.minioService.getFileUrl(
              file.minioFilename,
              process.env.MINIO_DEFAULT_BUCKETS,
            );
            return {
              ...file,
              url: presignedUrl,
            };
          }),
        );

        uploadedFileUrls = filesWithUrls;

        this.logger.log(`Successfully uploaded ${files.length} files`);
      }

      const uniqueCompanyId = await this.generateUniqueCompanyId();

      // Get legal entity details
      if (type) {
        const companyType = await this.companyTypeRepository.findOne({
          where: { name: type },
        });

        if (!companyType) {
          throw new NotAcceptableException(
            `Legal entity with name '${type}' not found.`,
          );
        }
        companyTypeForCompany = companyType;
      }

      // Check if company already exists
      const existingCompany = await this.companyRepository.isCompanyExist(
        companyName,
        companyTypeForCompany.id,
      );

      if (existingCompany) {
        throw new ConflictException(
          'Company with the same name and type already exists.',
        );
      }

      // 1️⃣ Create Account
      const account = queryRunner.manager.create(Account, {
        name: companyData.company.name,
        type: AccountTypes.BUSINESS,
        code: uniqueCompanyId,
      });

      const newAccount = await queryRunner.manager.save(account);
      // 2️⃣ Create Document Entry
      const documents = [];
      if (uploadedFileUrls && uploadedFileUrls.length > 0) {
        for (const fileUrl of uploadedFileUrls) {
          const document = queryRunner.manager.create(Document, {
            originalFilename: fileUrl.originalFilename,
            minioFilename: fileUrl.minioFilename,
            type: fileUrl.type,
          });
          const savedDocument = await queryRunner.manager.save(document);
          documents.push(savedDocument);
        }
      }

      // 3️⃣ Create AccountLegalDocument Entry

      // 2️⃣ Create Addresses
      const savedPrimaryAddress = await this.addressRepository.save({
        street: companyData.primaryAddress.street,
        city: companyData.primaryAddress.city,
        zipCode: companyData.primaryAddress.zipId,
        state: companyData.primaryAddress.state,
        zipcode: companyData.primaryAddress.zipcode,
      });

      let savedMailingAddress = savedPrimaryAddress; // Default to primaryAddress
      const isMailingAddressEmpty =
        !companyData.mailingAddress.street &&
        !companyData.mailingAddress.city &&
        !companyData.mailingAddress.state &&
        !companyData.mailingAddress.zipcode &&
        !companyData.mailingAddress.zipId;

      if (
        !companyData.isBothAddressSame ||
        companyData.isBothAddressSame === 'false'
      ) {
        const newMailingAddress = this.addressRepository.create({
          street: companyData.mailingAddress.street,
          city: companyData.mailingAddress.city,
          zipCode: companyData.mailingAddress.zipId,
          state: companyData.mailingAddress.state,
          zipcode: companyData.mailingAddress.zipcode,
        });
        savedMailingAddress = await this.addressRepository.save(
          newMailingAddress,
        );
      }

      // 3️⃣ Create Company
      // Note: code and name have been moved to Account entity
      const newCompany = queryRunner.manager.create(AccountDetail, {
        ...companyData.company,
        // Remove code and name as they're now in Account entity
        primaryAddress: savedPrimaryAddress.id,
        mailingAddress: isMailingAddressEmpty ? null : savedMailingAddress.id,
        account: newAccount.id,
        companyType: companyTypeForCompany.id,
        admins: [],
      });

      // Remove code property if it exists in the object
      if ('code' in newCompany) {
        delete newCompany.code;
      }

      const savedCompany = await queryRunner.manager.save(newCompany);
      for (const document of documents) {
        const accountLegalDocument = queryRunner.manager.create(
          AccountLegalDocument,
          {
            accountDetail: { id: savedCompany.id },
            document: { id: document.id },
          },
        );
        await queryRunner.manager.save(accountLegalDocument);
      }

      const adminUsers = [];
      const adminEmails = companyData.admins.map((admin) =>
        admin.emailId.toLowerCase(),
      );

      const uniqueEmails = new Set(adminEmails);

      if (uniqueEmails.size !== adminEmails.length) {
        throw new BadRequestException('Duplicate admin email(s) found');
      }
      // 4️⃣ Create Users and Assign Roles
      for (const adminData of companyData.admins) {
        const { firstName, lastName, emailId, phoneNumber } = adminData;
        const normalizedEmail = emailId.toLowerCase();

        // Find if there's any active user with this email
        let user = await this.userRepository.findOneBy({
          email: normalizedEmail,
          isDeleted: false,
        });

        if (user) {
          // Check if user is already a company admin somewhere
          const existingCompanyAdmin = await this.accountUserRepository
            .createQueryBuilder('accountUser')
            .innerJoin('accountUser.userRole', 'userRole')
            .innerJoin('userRole.role', 'role')
            .where('userRole.userId = :userId', { userId: user.id })
            .andWhere('role.name = :roleName', { roleName: 'company_admin' })
            .andWhere('accountUser.isDeleted = :isDeleted', {
              isDeleted: false,
            })
            .getOne();

          if (existingCompanyAdmin) {
            throw new ConflictException(`User ${emailId} is already exists`);
          }

          // Get company_admin role
          const companyAdminRole = await queryRunner.manager.findOne(Role, {
            where: { name: 'company_admin' },
          });
          if (!companyAdminRole) {
            throw new NotFoundException(`Role 'Company Admin' not found.`);
          }

          // Assign company_admin role
          const newUserRole = queryRunner.manager.create(UserRole, {
            userId: user.id,
            roleId: companyAdminRole.id,
            isActive: true,
            isDeleted: false,
          });

          const savedUserRole = await queryRunner.manager.save(newUserRole);

          // Create account user link
          const accountUser = queryRunner.manager.create(AccountUser, {
            accountId: newAccount.id,
            userRoleId: savedUserRole.id,
            isActive: true,
            isDeleted: false,
          });

          await queryRunner.manager.save(accountUser);
          this.logger.log(
            `Assigned company admin role to existing user ${emailId}`,
          );
        } else {
          // Handle deleted user case

          // Create completely new user
          user = queryRunner.manager.create(User, {
            firstName: firstName,
            lastName: lastName,
            username: normalizedEmail,
            email: normalizedEmail,
            contactNumber: phoneNumber,
            password: '',
            isActive: true,
            isDeleted: false,
          });

          const savedUser = await queryRunner.manager.save(user);

          // Create user role
          const companyAdminRole = await queryRunner.manager.findOne(Role, {
            where: { name: 'company_admin' },
          });
          if (!companyAdminRole) {
            throw new NotFoundException(`Role 'Company Admin' not found.`);
          }

          const userRole = queryRunner.manager.create(UserRole, {
            userId: savedUser.id,
            roleId: companyAdminRole.id,
            isActive: true,
            isDeleted: false,
          });

          const savedUserRole = await queryRunner.manager.save(userRole);

          // Create account user
          const accountUser = queryRunner.manager.create(AccountUser, {
            accountId: newAccount.id,
            userRoleId: savedUserRole.id,
            isActive: true,
            isDeleted: false,
          });

          await queryRunner.manager.save(accountUser);
          this.logger.log(
            `Created new user and assigned as company admin: ${emailId}`,
          );
        }

        // Add the created/updated user to the adminUsers array for future use
        adminUsers.push(user);
      }

      await queryRunner.manager.save(newCompany);

      await queryRunner.commitTransaction(); // ✅ Commit transaction before returning

      // 8️⃣ Send Emails After Transaction is Committed
      for (const admin of adminUsers) {
        (async () => {
          const {
            password: passwordForAdmin,
            hashedPassword: encodedPassword,
          } = await this.commonService.generateSecurePassword();

          // ✅ Update user password
          await this.userRepository.update(admin.id, {
            password: encodedPassword,
          });

          // ✅ Send email asynchronously
          this.sendPasswordByEmail(
            admin.username,
            companyData.company.name,
            null,
            passwordForAdmin,
          ).catch((err) =>
            this.logger.log(`Failed to send email to ${admin.username}:`, err),
          );
        })();
      }

      return {
        data: companyData,
        message: 'New Company created successfully',
      };
    } catch (error) {
      await queryRunner.rollbackTransaction(); // Rollback on error
      if (error instanceof HttpException) {
        // If it's already a HttpException, just rethrow it
        throw error;
      } else {
        // Otherwise, wrap it in a BadRequestException
        throw new BadRequestException(error.message);
      }
    } finally {
      await queryRunner.release(); // Always release the queryRunner
    }
  }

  // 🔹 Get company by company ID
  async findCompanyById(id: string): Promise<AccountDetail | null> {
    // Get basic company data from repository
    const companyData = await this.companyRepository.findCompanyById(id);

    // If we have admins, enhance them with all their roles
    if (companyData && companyData.admins && companyData.admins.length > 0) {
      // Process each admin to get their complete role information
      const enhancedAdmins = await Promise.all(
        companyData.admins.map(async (admin: any) => {
          // Get all roles for this user from all accounts
          const userRoles = await this.userRoleRepository.getUserRoleByUserId(
            admin.id,
          );

          // Extract unique role names
          const roleNames = [
            ...new Set(userRoles.map((role: any) => role.rolename)),
          ];

          // Check if user has multiple roles
          const hasMultipleRoles = roleNames.length > 1;

          // Return enhanced admin object with roles information
          return {
            ...admin,
            hasMultipleRoles,
            roles: roleNames,
          };
        }),
      );

      // Replace the admins array with the enhanced version
      companyData.admins = enhancedAdmins;
    }

    if (
      companyData &&
      companyData.legalDocUrl &&
      Array.isArray(companyData.legalDocUrl)
    ) {
      const documentWithUrls = await Promise.all(
        companyData.legalDocUrl.map(async (doc) => {
          let fileSize = 0;
          let url = null;
          let download = null;

          try {
            if (doc.minio_filename) {
              // Try to get file size
              try {
                fileSize = await this.minioService.getFileSize(
                  doc.minio_filename,
                  process.env.MINIO_DEFAULT_BUCKETS,
                );
              } catch (sizeError) {
                this.logger.error(
                  `Error getting file size for ${doc.minio_filename}: ${sizeError.message}`,
                );
                // Continue with size 0 if there's an error
              }

              // Try to get URLs
              try {
                url = await this.minioService.getFileUrl(
                  doc.minio_filename,
                  process.env.MINIO_DEFAULT_BUCKETS,
                  false,
                );
              } catch (urlError) {
                this.logger.error(
                  `Error getting URL for ${doc.minio_filename}: ${urlError.message}`,
                );
              }

              try {
                download = await this.minioService.getFileUrl(
                  doc.minio_filename,
                  process.env.MINIO_DEFAULT_BUCKETS,
                  true,
                );
              } catch (downloadError) {
                this.logger.error(
                  `Error getting download URL for ${doc.minio_filename}: ${downloadError.message}`,
                );
              }
            }
          } catch (error) {
            this.logger.error(
              `General error processing file ${doc.minio_filename}: ${error.message}`,
            );
          }

          return {
            ...doc,
            url,
            download,
            size: fileSize,
          };
        }),
      );

      companyData.legalDocUrl = documentWithUrls;
    }

    return companyData;
  }

  async findCompanies(
    pageOptions: PageOptionsDto,
    sortSearch: SortSearchDataDto,
    filters: CompanyFilterDto,
  ): Promise<PageDto<any>> {
    const page = Math.max(1, pageOptions.page || 1);
    const limit = Math.max(1, pageOptions.limit || 10);

    const { data, total, totalCompanies } =
      await this.companyRepository.findCompaniesWithFilters(
        filters.ids,
        sortSearch.searchText,
        filters.isActive,
        filters.companyName,
        filters.type,
        filters.zipcode,
        filters.city,
        filters.state,
        filters.startDate,
        filters.endDate,
        sortSearch.sortColumn || 'name',
        sortSearch.sortOrder?.toUpperCase() === 'DESC' ? 'DESC' : 'ASC',
        limit,
        page,
      );
    const pageOptionsForDto = {
      skip: page,
      limit: limit,
    };

    // **Meta Data for Pagination**
    const meta = new PageMetaDto({
      pageOptionsDto: pageOptionsForDto,
      itemCount: total,
      totalRecords: total,
      totalRecordsWithoutFilters: totalCompanies, // Add this new field
    });

    return new PageDto(data, meta);
  }
  async updateCompany(
    id: string,
    updateCompanyDto: any,
    files?: Express.Multer.File[],
  ): Promise<{ data: any; message: string }> {
    const queryRunner = this.datasource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const adminEmails =
        updateCompanyDto.admins?.map((admin) =>
          admin.emailId?.toLowerCase().trim(),
        ) || [];

      const duplicateEmails = adminEmails.filter(
        (email, index) => adminEmails.indexOf(email) !== index,
      );

      if (duplicateEmails.length > 0) {
        throw new BadRequestException(`Duplicate admin email(s) found`);
      }
      const existingAccount = await this.accountRepository.findOne({
        where: { id },
        relations: ['accountDetail'],
      });
      if (!existingAccount) {
        throw new NotFoundException('Account not found');
      }

      const accountDetailId = existingAccount?.accountDetail?.id;
      const existingCompany = await this.companyRepository.findOne({
        id: existingAccount?.accountDetail?.id,
      });
      if (!existingCompany) {
        throw new NotFoundException('AccountDetail not found');
      }
      const existingDocs = await queryRunner.manager
        .createQueryBuilder()
        .select([
          'ald.id as accountLegalDocId',
          'd.id as documentId',
          'd.minio_filename as minioFilename',
        ])
        .from(AccountLegalDocument, 'ald')
        .innerJoin('document', 'd', 'd.id = ald.document_id')
        .where('ald.account_detail_id = :companyId', {
          companyId: accountDetailId,
        })
        .getRawMany();

      // If there are documents to be deleted or replaced
      if (existingDocs && existingDocs?.length > 0) {
        for (const doc of existingDocs) {
          try {
            // Verify the document exists before deletion
            await queryRunner.manager.delete(AccountLegalDocument, {
              id: doc.accountlegaldocid,
            });

            // Delete AccountLegalDocument
            const documentResult = await queryRunner.manager.delete(Document, {
              id: doc.documentid,
            });

            this.logger.log(
              `AccountLegalDocument deletion query executed for id ${doc.accountLegalDocId}`,
            );
            this.logger.log(`documentResult deletion result:`, documentResult);

            if (documentResult.affected === 0) {
              throw new Error(
                `Failed to delete AccountLegalDocument with id ${doc.accountLegalDocId}`,
              );
            }
            await this.minioService.deleteFile(
              doc.miniofilename,
              process.env.MINIO_DEFAULT_BUCKETS,
            );

            // Delete Document
          } catch (error) {
            this.logger.error(`Error deleting document:`, error);
            throw error;
          }
        }
      }

      // Upload new document if provided
      if (files && files.length > 0) {
        // Upload files to MinIO
        const uploadResults = await Promise.all(
          files.map((file) =>
            this.minioService.uploadFile(
              file,
              process.env.MINIO_DEFAULT_BUCKETS,
            ),
          ),
        );

        // Create Document entries and AccountLegalDocument entries for each file
        for (const uploadResult of uploadResults) {
          // Create new Document entry
          const document = queryRunner.manager.create(Document, {
            originalFilename: uploadResult.originalFilename,
            minioFilename: uploadResult.minioFilename,
            type: uploadResult.type,
          });
          const savedDocument = await queryRunner.manager.save(document);

          // Create new AccountLegalDocument entry
          const accountLegalDocument = queryRunner.manager.create(
            AccountLegalDocument,
            {
              accountDetail: { id: accountDetailId },
              document: { id: savedDocument.id },
            },
          );
          await queryRunner.manager.save(accountLegalDocument);
        }
      }
      const updatedAccount = queryRunner.manager.create(Account, {
        name: updateCompanyDto.company.name,
        type: AccountTypes.BUSINESS,
      });

      await queryRunner.manager.update(
        Account,
        existingCompany.account.id,
        updatedAccount,
      );
      if (
        !existingCompany.companyType ||
        existingCompany.companyType.name !== updateCompanyDto.company.type
      ) {
        const companyType = await this.companyTypeRepository.findOne({
          where: { name: updateCompanyDto.company.type },
        });
        if (!companyType) {
          throw new NotAcceptableException(
            `Legal entity with name '${updateCompanyDto.company.type}' not found.`,
          );
        }
        updateCompanyDto.company.companyType = companyType;
      }
      let newMailingAddress = updateCompanyDto.mailingAddress;
      if (
        updateCompanyDto.primaryAddress &&
        existingCompany.primaryAddress &&
        existingCompany.primaryAddress.id
      ) {
        const primaryAddressUpdate = {
          street: updateCompanyDto.primaryAddress.street,
          city: updateCompanyDto.primaryAddress.city,
          state: updateCompanyDto.primaryAddress.state,
          zipcode: updateCompanyDto.primaryAddress.zipcode,
        };

        await queryRunner.manager.update(
          Address,
          existingCompany.primaryAddress.id,
          primaryAddressUpdate,
        );

        this.logger.log('Primary address updated:', {
          addressId: existingCompany.primaryAddress.id,
          updates: primaryAddressUpdate,
        });
      }

      // Check if mailing address is empty
      const isMailingAddressEmpty =
        !updateCompanyDto.mailingAddress?.street &&
        !updateCompanyDto.mailingAddress?.city &&
        !updateCompanyDto.mailingAddress?.state &&
        !updateCompanyDto.mailingAddress?.zipcode &&
        !updateCompanyDto.mailingAddress?.zipId;

      if (
        (!updateCompanyDto.isBothAddressSame ||
          updateCompanyDto.isBothAddressSame == 'false') &&
        updateCompanyDto.mailingAddress &&
        !isMailingAddressEmpty
      ) {
        const savingNewMailingAddress = this.addressRepository.create({
          street: updateCompanyDto.mailingAddress.street,
          city: updateCompanyDto.mailingAddress.city,
          zipCode: updateCompanyDto.mailingAddress.zipId,
          state: updateCompanyDto.mailingAddress.state,
          zipcode: updateCompanyDto.mailingAddress.zipcode,
        });
        newMailingAddress = await this.addressRepository.save(
          savingNewMailingAddress,
        );
        this.logger.log(
          'newMailingAddress while updating company',
          newMailingAddress,
        );
      }
      delete updateCompanyDto.company.type;
      delete updateCompanyDto.company.name;
      delete updateCompanyDto.company.code;
      await queryRunner.manager.update(AccountDetail, accountDetailId, {
        ...updateCompanyDto.company,
        legalDocURL: updateCompanyDto.legalDocURL,
        mailingAddress: isMailingAddressEmpty ? null : newMailingAddress?.id,
      });

      const newAdmins = updateCompanyDto.admins || [];
      const existingAdmins = await this.accountUserRepository.findCompanyAdmins(
        existingCompany.account.id,
      );
      const companyAdminRole = await this.roleRepository.findOneByField(
        'name',
        'company_admin',
      );

      if (!companyAdminRole) {
        throw new NotFoundException('Comapny Admin Role not found.');
      }
      // Process admins that need to be removed
      this.logger.log(
        `Processing admin removals. Existing: ${existingAdmins.admins.length}, New: ${newAdmins.length}`,
      );

      for (const admin of existingAdmins.admins) {
        // Check if this admin is not in the new admins list
        if (!newAdmins.some((newAdmin: any) => newAdmin.id === admin.id)) {
          this.logger.log(
            `Admin ${admin.username} is being removed from company`,
          );

          try {
            // Find the user role for this admin
            const userRole = await this.userRoleRepository.findOneBy({
              userId: admin.id,
              roleId: companyAdminRole.id,
              // Note: isDeleted is not in the type, but we'll check active roles only
            });

            if (!userRole || !userRole.isActive) {
              this.logger.log(
                `No active UserRole found for user: ${admin.username}`,
              );
              continue; // Skip to next admin
            }

            // Find the account user record
            const accountUser = await this.accountUserRepository.findOneBy({
              userRoleId: userRole.id,
              accountId: existingCompany.account.id,
              // Note: isDeleted is not in the type, but we'll check active account users only
            });

            if (!accountUser || !accountUser.isActive) {
              this.logger.log(
                `No active AccountUser found for user: ${admin.username}`,
              );
              continue; // Skip to next admin
            }

            // Mark the AccountUser as deleted
            this.logger.log(`Deactivating AccountUser for: ${admin.username}`);
            await queryRunner.manager.update(
              AccountUser,
              { id: accountUser.id },
              { isDeleted: true, isActive: false },
            );

            // Mark the UserRole as deleted
            this.logger.log(`Deactivating UserRole for: ${admin.username}`);
            await queryRunner.manager.update(
              UserRole,
              { id: userRole.id },
              { isDeleted: true, isActive: false },
            );

            // Check if this is the user's only role
            const roleCount =
              await this.userRoleRepository.getRoleCountByUserId(admin.id);

            if (roleCount <= 1) {
              // If this is the only role, mark the user as deleted too
              this.logger.log(
                `User ${admin.username} has no other roles, marking as deleted`,
              );
              await queryRunner.manager.update(
                User,
                { id: admin.id },
                { isDeleted: true, isActive: false },
              );
            } else {
              this.logger.log(
                `User ${admin.username} has ${roleCount} other roles, keeping user active`,
              );
            }

            this.logger.log(`Successfully removed admin: ${admin.username}`);
          } catch (error) {
            this.logger.error(
              `Error removing admin ${admin.username}: ${error.message}`,
            );
          }
        }
      }

      const adminUsers = [];

      // Create a map of existing admins for quick lookup
      const existingAdminsMap = new Map();
      existingAdmins.admins.forEach((admin) => {
        existingAdminsMap.set(admin.id, admin);
      });

      for (const adminData of newAdmins) {
        const normalizedEmail = adminData.emailId.toLowerCase();
        const existingAdmin = existingAdminsMap.get(adminData.id);

        // If this is an existing admin
        if (existingAdmin) {
          // Only check for email conflicts if email is being changed
          if (existingAdmin.email.toLowerCase() !== normalizedEmail) {
            // Check if the new email belongs to any other user
            const userWithNewEmail = await this.userRepository.findOneBy({
              email: normalizedEmail,
              isDeleted: false,
              id: Not(existingAdmin.id), // Exclude the current admin from the check
            });

            if (userWithNewEmail) {
              throw new BadRequestException(
                `Email ${normalizedEmail} is already exists`,
              );
            }

            // If no conflict, update the email along with other details
            await queryRunner.manager.update(
              User,
              { id: existingAdmin.id },
              {
                email: normalizedEmail,
                firstName: adminData.firstName,
                lastName: adminData.lastName,
                contactNumber: adminData.phoneNumber,
              },
            );
          } else {
            // If email hasn't changed, just update other details
            if (
              existingAdmin.firstName !== adminData.firstName ||
              existingAdmin.lastName !== adminData.lastName ||
              existingAdmin.contactNumber !== adminData.phoneNumber
            ) {
              await queryRunner.manager.update(
                User,
                { id: existingAdmin.id },
                {
                  firstName: adminData.firstName,
                  lastName: adminData.lastName,
                  contactNumber: adminData.phoneNumber,
                },
              );
            }
          }
          continue; // Skip to next admin
        }

        // For new admins, check if email already exists
        const userWithSameEmail = await this.userRepository.findOneBy({
          username: adminData.username.toLowerCase(),
          isDeleted: false,
        });

        if (userWithSameEmail) {
          // Check if user already has a company admin role
          const existingUserRole =
            await this.userRoleRepository.checkUserHasCompanyAdminRole(
              userWithSameEmail.id,
            );

          if (existingUserRole) {
            throw new ConflictException(
              `User ${normalizedEmail} is already exists.`,
            );
          }

          // Update user details and add company admin role
          if (
            userWithSameEmail.firstName !== adminData.firstName ||
            userWithSameEmail.lastName !== adminData.lastName ||
            userWithSameEmail.contactNumber !== adminData.phoneNumber
          ) {
            await queryRunner.manager.update(
              User,
              { id: userWithSameEmail.id },
              {
                firstName: adminData.firstName,
                lastName: adminData.lastName,
                contactNumber: adminData.phoneNumber,
              },
            );
          }

          // Create new company admin role for existing user
          const createUserRole = await queryRunner.manager.create(UserRole, {
            userId: userWithSameEmail.id,
            roleId: companyAdminRole.id,
            isActive: true,
            isDeleted: false,
            role: companyAdminRole,
            user: userWithSameEmail,
          });
          const savedUserRole = await queryRunner.manager.save(createUserRole);

          // Create account user link
          const createAccountUser = await queryRunner.manager.create(
            AccountUser,
            {
              accountId: existingCompany.account.id,
              isActive: true,
              isDeleted: false,
              userRole: savedUserRole,
              userRoleId: savedUserRole.id,
            },
          );

          await queryRunner.manager.save(createAccountUser);
          adminUsers.push(userWithSameEmail);
        } else {
          // Create new user
          const newUser = queryRunner.manager.create(User, {
            username: normalizedEmail,
            email: normalizedEmail,
            firstName: adminData.firstName,
            lastName: adminData.lastName,
            contactNumber: adminData.phoneNumber,
            password: '',
            isActive: true,
            isDeleted: false,
          });

          const savedAdmin = await queryRunner.manager.save(newUser);
          adminUsers.push(savedAdmin);

          // Create User Role
          const userRole = queryRunner.manager.create(UserRole, {
            userId: savedAdmin.id,
            roleId: companyAdminRole.id,
            isActive: true,
            isDeleted: false,
          });

          const savedUserRole = await queryRunner.manager.save(
            UserRole,
            userRole,
          );

          // Create AccountUser
          const accountUser = queryRunner.manager.create(AccountUser, {
            accountId: existingCompany.account.id,
            isActive: true,
            isDeleted: false,
            userRole: savedUserRole,
            userRoleId: savedUserRole.id,
          });

          await queryRunner.manager.save(AccountUser, accountUser);
          this.logger.log(`Added new admin: ${normalizedEmail}`);
        }
      }

      await queryRunner.commitTransaction();
      for (const admin of adminUsers) {
        (async () => {
          const {
            password: passwordForAdmin,
            hashedPassword: encodedPassword,
          } = await this.commonService.generateSecurePassword();

          // ✅ Update user password
          await this.userRepository.update(admin.id, {
            password: encodedPassword,
          });
          this.sendPasswordByEmail(
            admin.username,
            updateCompanyDto.company.name,
            null,
            passwordForAdmin,
          ).catch((err) =>
            this.logger.log(`Failed to send email to ${admin.username}:`, err),
          );
        })();
      }

      return {
        data: updateCompanyDto,
        message: 'Company updated successfully',
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      if (error instanceof HttpException) {
        // If it's already a HttpException, just rethrow it
        throw error;
      } else {
        // Otherwise, wrap it in a BadRequestException
        throw new BadRequestException(error.message);
      }
    } finally {
      await queryRunner.release();
    }
  }

  async findAllLegalEntities(): Promise<{ data: CompanyType[] }> {
    const entities = await this.companyTypeRepository.find({
      select: ['id', 'name'],
      order: {
        name: 'ASC', // Orders alphabetically Z to A
      },
    });
    return { data: entities }; // Wrap it in an object with a `data` key
  }

  async changeCompanyStatus(id: string, data: any): Promise<AccountDetail> {
    const company = await this.companyRepository.changeCompanyStatus(id, data);
    return company;
  }
  async downloadCompanyReport(
    sortSearch: SortSearchDataDto,
    filters: CompanyFilterDto,
    response: Response,
  ) {
    // Fetch entire company dataset (without pagination)
    const result = await this.companyRepository.findCompaniesWithFilters(
      filters.ids,
      sortSearch.searchText,
      filters.isActive,
      filters.companyName,
      filters.type,
      filters.zipcode,
      filters.city,
      filters.state,
      filters.startDate,
      filters.endDate,
      sortSearch.sortColumn || 'name',
      sortSearch.sortOrder?.toUpperCase() === 'DESC' ? 'DESC' : 'ASC',
      undefined, // No limit
      undefined, // No pagination
    );
    const { data } = result;

    if (!data.length) {
      return response
        .status(400)
        .json({ message: 'No data available to export' });
    }
    // Transform data to include only required fields
    const formattedData = data?.map((company, index) => {
      const adminNames =
        company.admins
          ?.map((admin, index) => {
            return `${company.admins.length > 1 ? `${index + 1}. ` : ''}${
              admin?.firstName || ''
            } ${admin?.lastName || ''}`;
          })
          .filter((name) => name.trim()) // Remove empty names
          .join('\r\n') || 'N/A';

      return {
        'Sr. No': index + 1,
        Company: company.name,
        'Market Centers Count': company.marketCentersCount || 0,
        'Legal Entity Type': company?.type || 'N/A',
        City:
          company.primaryAddress?.city || company.mailingAddress?.city || 'N/A',
        State:
          company.primaryAddress?.state ||
          company.mailingAddress?.state ||
          'N/A',
        'Company Admin Name(s)': adminNames,
        Status: company.isActive ? 'Active' : 'Inactive',
        'Created Date': company.createdAt
          ? new Date(company.createdAt).toLocaleDateString('en-US')
          : 'N/A',
      };
    });

    await this.excelService.generateExcelSheet({
      data: formattedData,
      response,
      fileName: 'Company_List',
    });
  }

  async deleteCompany(id: string): Promise<boolean> {
    const result = await this.companyRepository.deleteCompanyById(id);
    return result;
  }

  //  async getDashboard(companyId: string, dateRange: string) {
  //   // Parse dateRange to get startDate and endDate
  //   const { startDate, endDate } = this.companyRepository.parseDateRange(dateRange);

  //   // Fetch all metrics in parallel
  //   const [
  //     summary,
  //     jobs,
  //     revenueByCategories,
  //     totalCommission,
  //     topMarketCenters,
  //     topAgents,
  //   ] = await Promise.all([
  //     this.companyRepository.getSummary(companyId, startDate, endDate),
  //     this.companyRepository.getJobs(companyId, startDate, endDate),
  //     this.companyRepository.getRevenueByCategories(companyId, startDate, endDate),
  //     this.companyRepository.getTotalCommission(companyId, startDate, endDate),
  //     this.companyRepository.getTopMarketCenters(companyId, startDate, endDate),
  //     this.companyRepository.getTopAgents(companyId, startDate, endDate),
  //   ]);

  //   return {
  //     summary,
  //     jobs,
  //     revenueByCategories,
  //     totalCommission,
  //     topMarketCenters,
  //     topAgents,
  //   };
  // }
}
