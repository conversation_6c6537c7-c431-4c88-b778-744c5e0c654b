import { useContext } from 'react';
import { AuthContext } from '../../providers/auth-provider';
import AdminDashboard from '../companies/company/components/dashboard/dashboard';
import { ThemeContext } from '../../providers/theme-provider';
import MCMDashboard from '../companies/company/components/dashboard/McmDashboard';
// import AgentDashboard from './AgentDashboard';
// import ProviderDashboard from './ProviderDashboard';

const Dashboard =() => {

  const themeCtx = useContext(ThemeContext);
  if (!themeCtx) return null;
  const authContext = useContext(AuthContext)
  const role = authContext?.currentRole?.name ;
  console.log("🚀 ~ Dashboard ~ role:", role)

  if (!role) return <div className="p-4">Loading user...</div>;

  switch (role) {
    // case 'platform_admin':
    //   return <PlatformAdmin />;
    case 'company_admin':
      return <AdminDashboard />;
    case 'market_center_manager':
      return <MCMDashboard />;
    default:
      return <div className="p-4">Unknown role</div>;
  }
}
export default Dashboard;
