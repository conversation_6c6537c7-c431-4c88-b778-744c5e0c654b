import { NavLink, useLocation } from 'react-router-dom';
import { ReactComponent as DashboardIcon } from '../assets/icons/DashboardIcon.svg';
import { ReactComponent as ReportsIcon } from '../assets/icons/ReportsIcon.svg';
import {
  Bolt,
  BookCheck,
  Building,
  Building2,
  ChevronDown,
  CircleX,
  Menu,
  // ShieldHalf,
  // ShieldHalf,
} from 'lucide-react';
import { ReactComponent as AgentIcon } from '../assets/icons/AgentIcon.svg';
import { ReactComponent as HomeWorksIcon } from '../assets/icons/HomeworksIcon.svg';
import { ReactComponent as ServiceProvidersIcon } from '../assets/icons/ServiceProvidersIcon.svg';
import { ReactComponent as PropertiesIcon } from '../assets/icons/PropertiesIcon.svg';
import { ReactComponent as AdsIcon } from '../assets/icons/AdsIcon.svg';
import { useContext, useEffect, useRef, useState, useCallback } from 'react';
import { cn } from '@vast-application-framework/ui-components';
import { AuthContext } from '../providers/auth-provider';

export type Link = {
  title: string;
  to: string;
  icon?: (isActive: boolean) => JSX.Element;
  children?: Link[];
};

const roleTabs: Record<string, string[]> = {
  platform_admin: [
    'Dashboard',
    'Companies',
    'Catalog',
    'Market Centers',
    'Agents',
    'Service Providers',
    'Properties',
    'Ads Management',
    'Reports',
    'Configuration',
  ],
  company_admin: [
    'Dashboard',
    'Market Centers',
    'Agents',
    'Service Providers',
    'Properties',
    'Reports',
  ],
  market_center_manager: [
    'Dashboard',
    'Agents',
    'Service Providers',
    'Properties',
    'Reports',
  ],
};

const links: Link[] = [
  {
    title: 'Dashboard',
    to: '/dashboard',
    icon: (isActive: boolean) => (
      <DashboardIcon
        className={`${
          isActive ? 'text-white' : 'text-neutral-400'
        } fill-transparent w-5 h-5`}
      />
    ),
  },
  {
    title: 'Companies',
    to: '/companies',
    icon: (isActive: boolean) => (
      <Building2
        className={`${isActive ? 'text-white' : 'text-neutral-400'} w-5 h-5`}
      />
    ),
  },
  {
    title: 'Catalog',
    to: '/catalog',
    icon: (isActive: boolean) => (
      <BookCheck
        className={`${isActive ? 'text-white' : 'text-neutral-400'} w-5 h-5`}
      />
    ),
  },
  {
    title: 'Market Centers',
    to: '/market-centers',
    icon: (isActive: boolean) => (
      <Building
        className={`${isActive ? 'text-white' : 'text-neutral-400'} w-5 h-5`}
      />
    ),
  },
  {
    title: 'Agents',
    to: '/agents',
    icon: (isActive: boolean) => (
      <AgentIcon
        className={`${
          isActive ? 'text-white' : 'text-neutral-400'
        } fill-transparent w-5 h-5`}
      />
    ),
  },
  {
    title: 'Service Providers',
    to: '/service-providers',
    icon: (isActive: boolean) => (
      <ServiceProvidersIcon
        className={`${
          isActive ? 'text-white' : 'text-neutral-400'
        } fill-transparent w-5 h-5`}
      />
    ),
  },
  {
    title: 'Properties',
    to: '/properties',
    icon: (isActive: boolean) => (
      <PropertiesIcon
        className={`${
          isActive ? 'text-white' : 'text-neutral-400'
        } fill-transparent w-5 h-5`}
      />
    ),
  },
  {
    title: 'Ads Management',
    to: '/ads-management',
    icon: (isActive: boolean) => (
      <AdsIcon
        className={`${isActive ? 'text-white' : 'text-neutral-400'} w-5 h-4`}
      />
    ),
  },
  {
    title: 'Reports',
    to: '/reports',
    icon: (isActive: boolean) => (
      <ReportsIcon
        className={`${
          isActive ? 'text-white' : 'text-neutral-400'
        } fill-transparent w-5 h-5`}
      />
    ),
  },
  {
    title: 'Configuration',
    to: '/configurations',
    icon: (isActive: boolean) => (
      <Bolt
        className={`${isActive ? 'text-white' : 'text-neutral-400'} w-5 h-5`}
      />
    ),
    children: [
      {
        title: 'Asset Type',
        to: '/configurations',
      },
    ],
  },
];

type SidebarPropsType = {
  sidebarOpen: boolean;
  setSidebarOpen: React.Dispatch<React.SetStateAction<boolean>>;
  className?: string;
};

const Sidebar = ({
  sidebarOpen,
  setSidebarOpen,
  className,
}: SidebarPropsType) => {
  const { pathname } = useLocation();
  const sidebar = useRef<HTMLElement>(null);

  const authContext = useContext(AuthContext);

  const currentRole = authContext?.currentRole ?? { name: '' };

  // Collect all allowed tabs from only one current role
  const allowedTabs = roleTabs[currentRole.name] || [];

  // Filter links based on allowed tabs
  const filteredLinks = links.filter((link) =>
    allowedTabs.includes(link.title),
  );

  return (
    <aside
      ref={sidebar}
      className={cn(
        `absolute top-0 left-0 bottom-0 z-50 lg:flex lg:min-h-full min-w-[220px] w-[220px] max-w-[220px] px-[16px] pt-[16px] lg:w-[270px] lg:flex-col gap-[40px] overflow-y-hidden bg-primary duration-300 ease-linear lg:static lg:translate-x-0 
      ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`,
        className,
      )}
    >
      <div className="h-10 w-full flex flex-row items-center gap-2 lg:sticky lg:top-0">
        <HomeWorksIcon className="w-10 h-10 text-white" />
        <span className="text-white text-[20px]">
          {import.meta.env.VITE_APP_NAME}
        </span>
        <button
          className="lg:hidden text-white"
          onClick={() => setSidebarOpen(!sidebarOpen)}
        >
          {sidebarOpen ? <CircleX /> : <Menu />}
        </button>
      </div>
      <RenderNavLinks links={filteredLinks} pathname={pathname} />
    </aside>
  );
};

export default Sidebar;

const NavItem = ({
  title,
  to,
  icon,
  children,
  pathname,
  isNested,
  navClassName,
}: {
  title: string;
  to: string;
  icon?: (isActive: boolean) => JSX.Element;
  children?: Link[];
  pathname: string;
  isNested?: boolean;
  navClassName?: string;
}) => {
  // Initialize isOpen state based on whether this is the Configuration item
  const [isOpen, setIsOpen] = useState(
    title === 'Configuration' && pathname.startsWith('/configurations'),
  );
  const location = useLocation();

  // Always keep Configuration dropdown open when on configurations page
  useEffect(() => {
    if (title === 'Configuration' && pathname.startsWith('/configurations')) {
      setIsOpen(true);
    }
  }, [pathname, title]);

  const handleClick = (e: React.MouseEvent) => {
    if (children?.length) {
      e.preventDefault();
      setIsOpen(!isOpen);
      return;
    }

    if (to === '/market-centers') {
      e.preventDefault();
      window.location.href = '/market-centers';
      return;
    }
  };

  // Determine if this nav item is active
  const isActive = useCallback(() => {
    if (to === '/market-centers') {
      return pathname.startsWith('/market-centers') && !location.search;
    }
    if (to === '/companies') {
      return (
        pathname.startsWith('/companies') ||
        (pathname === '/market-centers' &&
          location.search.includes('accountId'))
      );
    }
    return pathname.startsWith(to);
  }, [location.search, pathname, to]);

  return (
    <div key={to} className="relative">
      {isNested && (
        <span className="absolute -top-[10%] -translate-y-1/2 -left-3.5 w-2.5 h-12 -z-10 border-l border-b border-hw-neutral-400" />
      )}
      <NavLink
        to={to}
        end={to === '/market-centers'}
        onClick={handleClick}
        className={({ isActive: _linkActive }) => {
          const isCurrentActive = isActive();
          return cn(
            `flex flex-row w-full h-[45px] items-center overflow-hidden justify-between gap-2 rounded-[8px] ${
              isCurrentActive
                ? ` ${
                    isNested
                      ? 'bg-white text-hw-accent '
                      : 'bg-hw-accent text-white'
                  }`
                : 'text-neutral-400'
            } hover:bg-neutral-700 hover:text-neutral-400`,
            navClassName,
          );
        }}
      >
        {({ isActive: _isActive }) => (
          <>
            {children?.length ? (
              <button
                className="w-full h-full p-[12px] flex flex-row items-center justify-between"
                onClick={(e) => {
                  e.preventDefault();
                  setIsOpen(!isOpen);
                }}
              >
                <div className="flex flex-row items-center gap-2 h-full">
                  {icon ? icon(isActive()) : null}
                  <span className="text-[14px]">{title}</span>
                </div>
                <ChevronDown
                  size={18}
                  className={`transition-transform ${
                    isOpen ? 'rotate-180' : ''
                  }`}
                />
              </button>
            ) : (
              <div className="p-[12px] flex flex-row items-center gap-2 h-full">
                {icon ? icon(isActive()) : null}
                <span className="text-[14px]">{title}</span>
              </div>
            )}
          </>
        )}
      </NavLink>

      {isOpen && children && children.length > 0 && (
        <div className="flex flex-col gap-2 justify-center pl-[34px] mt-5">
          {children.map((child) => (
            <NavItem
              key={child.to}
              {...child}
              pathname={pathname}
              isNested={true}
              navClassName={'h-[34px]'}
            />
          ))}
        </div>
      )}
    </div>
  );
};

// Renders the top-level nav links
const RenderNavLinks = ({
  links,
  pathname,
}: {
  links: Link[];
  pathname: string;
}) => {
  return (
    <div className="flex h-[calc(100dvh-110px)] flex-col gap-[8px] pb-5 overflow-y-auto no-scrollbar">
      {links.map((link) => (
        <NavItem key={link.to} {...link} pathname={pathname} isNested={false} />
      ))}
    </div>
  );
};
