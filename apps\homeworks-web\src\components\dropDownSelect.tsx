import React, { useEffect, useState, useRef } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@shadcn/ui';
import { Check, ChevronDown, Search } from 'lucide-react';
import { cn } from '@vast-application-framework/ui-components';

interface DropdownSelectProps {
  options: { label: string; value: string }[];
  placeholder?: string;
  isMulti?: boolean;
  onChange: (selected: string[] | string) => void;
  onSearch?: (searchText: string) => void;
  containerClassName?: string;
  componentClassName?: string;
  multiDropdownClass?: string;
  singleSelectClass?: string;
  showSelelctedOpt: boolean;
  menuContentClass?: string;
  initialValue?: string[];
  onOpen?: () => void;
  disabled?: boolean;
  value?: string | string[];
  FooterComponent?: React.ReactNode;
  maintainOrder?: boolean; // Add this prop
}

const DropdownSelect: React.FC<DropdownSelectProps> = ({
  options,
  placeholder,
  isMulti,
  onChange,
  onSearch,
  containerClassName,
  componentClassName,
  multiDropdownClass,
  singleSelectClass,
  showSelelctedOpt,
  menuContentClass,
  initialValue,
  disabled,
  value,
  FooterComponent,
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [selectedValues, setSelectedValues] = useState<string[]>(initialValue ?? []);
  const [tempSelectedValues, setTempSelectedValues] = useState<string[]>([]);
  // Initialize temp selection when dropdown opens
  useEffect(() => {
    if (isOpen) {
      setTempSelectedValues([...selectedValues]);
    }
  }, [isOpen]);

  // Commit selections when dropdown closes
  const handleOpenChange = (open: boolean) => {
    if (!disabled) {
      setIsOpen(open);
      if (!open) {
        if (isMulti) {
          setSelectedValues(tempSelectedValues);
          onChange(tempSelectedValues);
        }
        setSearchTerm('');
        onSearch?.('');
      }
    }
  };

  const [searchTerm, setSearchTerm] = useState<string>('');
  const dropdownContentRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [selectedItemsGroup, setSelectedItemsGroup] = useState<string[]>([]);
  const [unselectedItemsGroup, setUnselectedItemsGroup] = useState<string[]>([]);

  useEffect(() => {
    if (value) {
      setSelectedValues(Array.isArray(value) ? value : [value]);
    }
  }, [value]);

  useEffect(() => {
    if (isOpen) {
      searchInputRef.current?.focus();
    }
  }, [isOpen]);

  useEffect(() => {
    if (isOpen) {
      // On open, organize items into selected and unselected groups
      const selected = options
        .filter(opt => selectedValues.includes(opt.value))
        .map(opt => opt.value);
      const unselected = options
        .filter(opt => !selectedValues.includes(opt.value))
        .map(opt => opt.value);
      
      setSelectedItemsGroup(selected);
      setUnselectedItemsGroup(unselected);
    }
  }, [isOpen, options, selectedValues]);

  const handleSelect = (value: string) => {
    if (disabled) return;

    if (isMulti) {
      const isDeselect = tempSelectedValues.includes(value);
      
      if (isDeselect) {
        // Handle deselection immediately for both temp and actual values
        setTempSelectedValues(prev => prev.filter(v => v !== value));
        setSelectedValues(prev => prev.filter(v => v !== value));
        onChange(selectedValues.filter(v => v !== value));
        
        // Update groups immediately for deselection
        setSelectedItemsGroup(prev => prev.filter(v => v !== value));
        setUnselectedItemsGroup(prev => [...prev, value]);
      } else {
        // Only update temp selection for new selections
        setTempSelectedValues(prev => [...prev, value]);
      }
    } else {
      // For single select, update immediately and close dropdown
      setSelectedValues([value]);
      onChange(value); // Pass single value for single select
      setIsOpen(false);
    }
  };

  const filteredOptions = options.filter((option) => {
    // Get the normalized search term (trimmed but preserving internal spaces)
    const normalizedSearchTerm = searchTerm.trim().toLowerCase();
    
    // If there's no actual search content, don't filter
    if (normalizedSearchTerm === '') return true;
    
    // Search with the normalized term
    return option.label.toLowerCase().includes(normalizedSearchTerm);
  });

  const renderDropdownContent = () => {
    return (
      <div className="overflow-y-auto">
        {/* Selected Items Group */}
        {selectedItemsGroup.length > 0 && (
          <div className="border-b border-gray-200">
            {selectedItemsGroup.map((value) => {
              const option = options.find(opt => opt.value === value);
              if (!option) return null;
              return renderDropdownItem(option, true);
            })}
          </div>
        )}

        {/* Unselected Items Group */}
        <div>
          {unselectedItemsGroup.map((value) => {
            const option = options.find(opt => opt.value === value);
            if (!option) return null;
            return renderDropdownItem(option, false);
          })}
        </div>
      </div>
    );
  };

  // Helper function to render individual dropdown items
  const renderDropdownItem = (option: { label: string; value: string }, isSelected: boolean) => {
    // Use tempSelectedValues for visual state
    const isItemSelected = tempSelectedValues.includes(option.value);
    return (
      <DropdownMenuItem
        key={option.value}
        onClick={() => handleSelect(option.value)}
        {...(isMulti ? { onSelect: (e) => e.preventDefault() } : {})}
        className={cn(
          `flex items-center space-x-2 cursor-pointer py-2 px-3 ${
            isItemSelected ? 'bg-[#E2E2FF]' : 'hover:bg-hw-primary-100'
          }`,
          componentClassName,
        )}
      >
        {isMulti ? (
          <div
            className={cn(
              'w-4 h-4 flex items-center justify-center border rounded-sm',
              isItemSelected
                ? 'bg-hw-primary border-hw-primary text-white'
                : 'border-gray-400 bg-white',
              multiDropdownClass,
            )}
          >
            {isItemSelected && <Check className="w-4 h-4 text-white" />}
          </div>
        ) : (
          <div
            className={cn(
              `w-4 h-4 rounded-full border ${
                isSelected
                  ? 'bg-hw-primary border-hw-primary-100'
                  : 'border-gray-400'
              }`,
              singleSelectClass,
            )}
          />
        )}
        <span className="text-xs text-hw-neutral-850">
          {option.label}
        </span>
      </DropdownMenuItem>
    );
  };

  return (
    <DropdownMenu onOpenChange={handleOpenChange}>
      <DropdownMenuTrigger asChild>
        <button
          className={cn(
            'w-full flex justify-between items-center border-[1px] px-4 py-2 h-12 min-h-10 ',
            isOpen
              ? 'border-[#5D54EA] bg-white text-[#000000]'
              : 'border-[#E4E4E8] bg-white text-[#000000]',
            containerClassName,
            disabled ? 'cursor-not-allowed opacity-50' : '',
          )}
          onClick={(e) => {
            if (disabled) {
              e.preventDefault();
              e.stopPropagation();
            }
          }}
        >
          <div className="flex flex-wrap items-center gap-1 flex-1 ">
            {showSelelctedOpt && selectedValues.length > 0 ? (
              isMulti ? (
                <>
                  {selectedValues.slice(0, 1).map((val) => {
                    const label = options.find((o) => o.value === val)?.label;

                    return (
                      <div
                        key={val}
                        className="flex items-center bg-[#E2E2FF] text-[#5D54EA] px-2 py-1 rounded-full text-xs max-w-fit truncate flex-shrink-0"
                      >
                        {label}
                      </div>
                    );
                  })}
                  {selectedValues.length > 1 && (
                    <span className="text-xs text-[#9392A3] truncate">
                      +{selectedValues.length - 1} more
                    </span>
                  )}
                </>
              ) : (
                <span className="truncate text-sm ">
                  {options.find((o) => o.value === selectedValues[0])?.label}
                </span>
              )
            ) : (
              <span className="text-[#9392A3] text-sm font-normal truncate">
                {placeholder}
              </span>
            )}
          </div>
          <ChevronDown className="w-4 h-4 ml-2 flex-shrink-0" />
        </button>
      </DropdownMenuTrigger>

      {isOpen && (
        <DropdownMenuContent
          className={cn(
            'w-full shadow-lg bg-white border-[1px] border-hw-primary p-0 rounded-md overflow-y-auto overflow-x-hidden max-h-[300px] pr-2', // <-- Add pr-2
            '[&::-webkit-scrollbar]:w-1',
            '[&::-webkit-scrollbar-track]:bg-gray-100',
            '[&::-webkit-scrollbar-track]:my-0.5',
            '[&::-webkit-scrollbar-track]:rounded-full',
            '[&::-webkit-scrollbar-thumb]:bg-gray-300',
            '[&::-webkit-scrollbar-thumb]:rounded-full',
            // 'dark:[&::-webkit-scrollbar-track]:bg-neutral-700',
            // 'dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500',
            //added scrollbar
            menuContentClass,
          )}
          ref={dropdownContentRef}
          onCloseAutoFocus={(e) => e.preventDefault()}
        >
          <div className="flex items-center gap-2 px-2 py-2 border-b border-gray-200">
            <Search className="w-4 h-4 text-[#5D54EA]" />
            <input
              ref={searchInputRef}
              type="text"
              placeholder="Search"
              className="w-full p-1 text-sm bg-transparent outline-none"
              value={searchTerm}
              onChange={(e) => {
                const text = e.target.value;
                setSearchTerm(text);
                
                // Check if there's any non-space content
                if (text.trim() !== '') {
                  // If there's actual text, search with the text but trim leading/trailing spaces
                  onSearch?.(text.trim());
                } else {
                  // If it's only spaces or empty, clear the search
                  onSearch?.('');
                }
              }}
              disabled={disabled}
              onMouseDown={(e) => e.stopPropagation()}
              onClick={(e) => e.stopPropagation()}
              onKeyDown={(e) => e.stopPropagation()}
            />
          </div>

          {/* Render content with search filtering */}
          {searchTerm.trim() !== '' ? (
            <div className="overflow-y-auto">
              {filteredOptions.map((option) =>
                renderDropdownItem(
                  option,
                  selectedValues.includes(option.value),
                ),
              )}
            </div>
          ) : (
            renderDropdownContent()
          )}

          {/* Footer component if exists */}
          {FooterComponent && (
            <div onClick={(e) => e.stopPropagation()}>{FooterComponent}</div>
          )}
        </DropdownMenuContent>
      )}
    </DropdownMenu>
  );
};

export default DropdownSelect;
