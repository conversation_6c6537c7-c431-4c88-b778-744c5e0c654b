import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

export class CreateInspectionDto {
  @ApiProperty({ maxLength: 50 })
  @IsNotEmpty()
  providerName: string;

  @ApiProperty()
  code: string;

  @ApiProperty()
  @IsNotEmpty()
  inspectionDate: Date;

  @ApiProperty()
  description: string;

  @ApiProperty({ type: 'file', format: 'binary', required: false })
  @IsOptional()
  inspectionDoc: Express.Multer.File;
}
