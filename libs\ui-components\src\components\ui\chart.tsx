'use client';

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  Tooltip,
  CartesianGrid,
} from 'recharts';
import React from 'react';

// -------- DonutChartCard --------
export const DonutChartCard = ({
  data,
}: {
  data: { name: string; value: number; color: string }[];
}) => (
  <div className="h-[200px]">
    <ResponsiveContainer width="100%" height="100%">
      <PieChart>
        <Pie
          data={data}
          dataKey="value"
          nameKey="name"
          innerRadius={50}
          outerRadius={80}
          paddingAngle={2}
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={entry.color} />
          ))}
        </Pie>
      </PieChart>
    </ResponsiveContainer>
  </div>
);

// -------- Chart Types & Components --------
export type ChartConfig = {
  [key: string]: {
    label: string;
    color: string;
  };
};

export const ChartContainer = ({
  config,
  className,
  children,
}: {
  config: ChartConfig;
  className?: string;
  children: React.ReactNode;
}) => {
  return (
    <div
      className={`rounded-xl border bg-white p-4 shadow-md w-full ${className}`}
      style={{
        // Pass chart colors to CSS vars for recharts fill
        ...Object.entries(config).reduce((acc, [key, value]) => {
          (acc as any)[`--color-${key}`] = value.color;
          return acc;
        }, {} as React.CSSProperties),
      }}
    >
      {children}
    </div>
  );
};

export const ChartTooltipContent = ({
  active,
  payload,
}: {
  active?: boolean;
  payload?: any;
}) => {
  if (!active || !payload?.length) return null;

  return (
    <div className="rounded-md border bg-white px-3 py-2 text-sm shadow-sm">
      {payload.map((item: any, idx: number) => (
        <div key={idx} className="flex items-center gap-2">
          <span
            className="h-2 w-2 rounded-full"
            style={{ backgroundColor: item.color }}
          />
          <span className="text-muted-foreground">
            {item.name}: <strong>{item.value}</strong>
          </span>
        </div>
      ))}
    </div>
  );
};

export const ChartTooltip = (props: any) => {
  return <Tooltip {...props} wrapperClassName="!outline-none" />;
};

export const ChartLegendContent = ({ config }: { config: ChartConfig }) => (
  <div className="flex flex-wrap gap-4 text-sm mt-4">
    {Object.entries(config).map(([key, { label, color }]) => (
      <div key={key} className="flex items-center gap-2">
        <span
          className="h-2 w-2 rounded-full"
          style={{ backgroundColor: color }}
        />
        <span>{label}</span>
      </div>
    ))}
  </div>
);

export const ChartLegend = ({ config }: { config: ChartConfig }) => {
  return <ChartLegendContent config={config} />;
};


// export {
//   DonutChartCard,
//   ChartConfig,
//   ChartContainer,
//   ChartTooltip,
//   ChartTooltipContent,
//   ChartLegend,
//   ChartLegendContent,
// };
