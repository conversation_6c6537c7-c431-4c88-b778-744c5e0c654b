import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { AgentRepository } from './repositories/agent.repository';
import { CreateAgentDto } from './dto/create-agent.dto';
import { UpdateAgentDto } from './dto/update-agent.dto';
import { AgentDetail } from './entities/agent.entity';
import { CodeGeneratorService, ExcelService } from '@common';
import { Identifier } from '@shared-types';
import { GovDocumentType } from './entities/gov-document-type.entity';
import { AddressProofDocumentType } from './entities/address_proof_document_type.entity';
import { AgentFilterDto } from './dto/agent-filter.dto';
import { ActivateDeactivateAgentDto } from './dto/activate-deactivate-agent.dto';
import { PageOptionsDto } from '../common/pagination/pageOptions.dto';
import { SortSearchDataDto } from '../common/pagination/sort-search-data.dto';
import { PageDto } from '../common/pagination/page.dto';
import { Response } from 'express';
import { MinioService } from '../common/minio/minio.service';
import { AgentExperienceOption } from './entities/agent_experience_option.entity';

@Injectable()
export class AgentService {
  private readonly logger = new Logger(AgentService.name);

  constructor(
    private readonly agentRepository: AgentRepository,
    private readonly codeGeneratorService: CodeGeneratorService,
    private readonly excelService: ExcelService,
    private readonly minioService: MinioService,
  ) {}

  async createAgent(
    createAgentDto: CreateAgentDto,
    managerUser?: any,
  ): Promise<{ data: AgentDetail; message: string }> {
    try {
      const code = await this.codeGeneratorService.generateUniqueCode({
        prefix: 'AGT',
        length: 6,
        repository: this.agentRepository,
      });

      const agent = await this.agentRepository.createAgent(
        {
          ...createAgentDto,
          code,
        },
        managerUser,
      );

      return {
        data: agent,
        message: 'Agent created successfully!',
      };
    } catch (error) {
      // Don't log the error again if it's already been logged
      if (!(error instanceof NotFoundException)) {
        this.logger.error(`Error in createAgent: ${error.message}`);
      }
      throw error;
    }
  }

  async updateAgent(
    id: Identifier,
    updateAgentDto: UpdateAgentDto,
  ): Promise<{ data: AgentDetail; message: string }> {
    try {
      const agent = await this.agentRepository.findAgentById(id);
      if (!agent) {
        throw new NotFoundException(`Agent with ID ${id} not found`);
      }

      const updatedAgent = await this.agentRepository.updateAgent(
        id,
        updateAgentDto,
      );

      return {
        data: updatedAgent,
        message: 'Agent updated successfully!',
      };
    } catch (error) {
      this.logger.error(`Error in updateAgent: ${error.message}`);
      throw error;
    }
  }

  async findAgentById(
    id: Identifier,
  ): Promise<{ data: AgentDetail; message: string }> {
    try {
      const agent = await this.agentRepository.findAgentById(id);

      if (agent.user && agent.user.profileImageUrl) {
        try {
          let profileImageData;

          // Check if profileImageUrl is a string that contains JSON
          if (typeof agent.user.profileImageUrl === 'string') {
            try {
              profileImageData = JSON.parse(
                agent.user.profileImageUrl as string,
              );

              // Add size to the parsed object if it has a minioFilename
              if (profileImageData && profileImageData.minioFilename) {
                try {
                  profileImageData.size = await this.minioService.getFileSize(
                    profileImageData.minioFilename,
                    process.env.MINIO_DEFAULT_BUCKETS || 'company',
                  );
                } catch (sizeError) {
                  this.logger.warn(
                    `Error getting file size for profileImageUrl: ${sizeError.message}`,
                  );
                  profileImageData.size = 1024 * 1024; // Default to 1MB
                }

                // Update the profileImageUrl with the enhanced object
                agent.user.profileImageUrl = JSON.stringify(profileImageData);
              }
            } catch (parseError) {
              this.logger.warn(
                `Error parsing profileImageUrl: ${parseError.message}`,
              );
            }
          }
        } catch (error) {
          this.logger.warn(
            `Error processing profileImageUrl: ${error.message}`,
          );
        }
      }

      if (agent.profLicenseDoc && agent.profLicenseDoc.minioFilename) {
        try {
          (agent.profLicenseDoc as any).size =
            await this.minioService.getFileSize(
              agent.profLicenseDoc.minioFilename,
              process.env.MINIO_DEFAULT_BUCKETS || 'company',
            );
          
          // Add download URL
          (agent.profLicenseDoc as any).download =
            await this.minioService.getFileUrl(
              agent.profLicenseDoc.minioFilename,
              process.env.MINIO_DEFAULT_BUCKETS || 'company',
              true, // Force download
            );
        } catch (error) {
          this.logger.warn(
            `Error processing profLicenseDoc: ${error.message}`,
          );
          (agent.profLicenseDoc as any).size = 1024 * 1024; // Default to 1MB
        }
      }

      // Enhance document objects with file size and download URL
      if (agent.govDocument && agent.govDocument.minioFilename) {
        try {
          (agent.govDocument as any).size = await this.minioService.getFileSize(
            agent.govDocument.minioFilename,
            process.env.MINIO_DEFAULT_BUCKETS || 'company',
          );
          
          // Add download URL
          (agent.govDocument as any).download = await this.minioService.getFileUrl(
            agent.govDocument.minioFilename,
            process.env.MINIO_DEFAULT_BUCKETS || 'company',
            true, // Force download
          );
        } catch (error) {
          this.logger.warn(
            `Error processing govDocument: ${error.message}`,
          );
          (agent.govDocument as any).size = 1024 * 1024; // Default to 1MB
        }
      }

      if (
        agent.addressProofDocument &&
        agent.addressProofDocument.minioFilename
      ) {
        try {
          (agent.addressProofDocument as any).size =
            await this.minioService.getFileSize(
              agent.addressProofDocument.minioFilename,
              process.env.MINIO_DEFAULT_BUCKETS || 'company',
            );
          
          // Add download URL
          (agent.addressProofDocument as any).download =
            await this.minioService.getFileUrl(
              agent.addressProofDocument.minioFilename,
              process.env.MINIO_DEFAULT_BUCKETS || 'company',
              true, // Force download
            );
        } catch (error) {
          this.logger.warn(
            `Error processing addressProofDocument: ${error.message}`,
          );
          (agent.addressProofDocument as any).size = 1024 * 1024; // Default to 1MB
        }
      }

      return {
        data: agent,
        message: 'Agent retrieved successfully!',
      };
    } catch (error) {
      this.logger.error(`Error in findAgentById: ${error.message}`);
      throw error;
    }
  }

  async findAgentByCode(
    code: string,
  ): Promise<{ data: AgentDetail; message: string }> {
    try {
      const agent = await this.agentRepository.findAgentByCode(code);
      if (!agent) {
        throw new NotFoundException(`Agent with code ${code} not found`);
      }
      return {
        data: agent,
        message: 'Agent retrieved successfully!',
      };
    } catch (error) {
      this.logger.error(`Error in findAgentByCode: ${error.message}`);
      throw error;
    }
  }

  async findAllAgents(
    pageOptionsDto: PageOptionsDto,
    sortSearchDataDto: SortSearchDataDto,
    filters: AgentFilterDto,
    marketCenterId?: string,
    accountId?: string,
  ): Promise<PageDto<AgentDetail>> {
    try {
      const result = await this.agentRepository.findAllAgents(
        pageOptionsDto,
        sortSearchDataDto,
        filters,
        marketCenterId,
        accountId,
      );

      return {
        data: result.data,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Error in findAllAgents: ${error.message}`);
      throw error;
    }
  }

  async findAgentsByMarketCenter(
    marketCenterId: Identifier,
  ): Promise<{ data: AgentDetail[]; message: string }> {
    try {
      const agents = await this.agentRepository.findAgentsByMarketCenter(
        marketCenterId,
      );
      return {
        data: agents,
        message: 'Agents retrieved successfully!',
      };
    } catch (error) {
      this.logger.error(`Error in findAgentsByMarketCenter: ${error.message}`);
      throw error;
    }
  }

  async findAgentsByAccount(
    accountId: Identifier,
  ): Promise<{ data: AgentDetail[]; message: string }> {
    try {
      const agents = await this.agentRepository.findAgentsByAccount(accountId);
      return {
        data: agents,
        message: 'Agents retrieved successfully!',
      };
    } catch (error) {
      this.logger.error(`Error in findAgentsByAccount: ${error.message}`);
      throw error;
    }
  }

  async getAllGovDocuments(): Promise<{
    data: GovDocumentType[];
    message: string;
  }> {
    try {
      const govDocuments = await this.agentRepository.findAllGovDocuments();

      return {
        data: govDocuments,
        message: 'Government document types retrieved successfully!',
      };
    } catch (error) {
      this.logger.error(`Error in getAllGovDocuments: ${error.message}`);
      throw error;
    }
  }

  async getAllAddressProofDocuments(): Promise<{
    data: AddressProofDocumentType[];
    message: string;
  }> {
    try {
      const addressProofDOcuments =
        await this.agentRepository.findAllAddressProofDocuments();

      return {
        data: addressProofDOcuments,
        message: 'Address proof document types retrieved successfully!',
      };
    } catch (error) {
      this.logger.error(
        `Error in getAllAddressProofDocuments: ${error.message}`,
      );
      throw error;
    }
  }

  async changeAgentStatus(
    id: string,
    activateDeactivateAgentDto: ActivateDeactivateAgentDto,
  ): Promise<{ data: AgentDetail; message: string }> {
    try {
      const updatedAgent = await this.agentRepository.changeAgentStatus(
        id,
        activateDeactivateAgentDto.isActive,
      );

      return {
        data: updatedAgent,
        message: `Agent ${
          activateDeactivateAgentDto.isActive ? 'activated' : 'deactivated'
        } successfully`,
      };
    } catch (error) {
      this.logger.error(`Error in changeAgentStatus: ${error.message}`);
      throw error;
    }
  }

  async downloadAgentReport(
    sortSearchDataDto: SortSearchDataDto,
    filterAgentDto: AgentFilterDto,
    response: Response,
    marketCenterId?: string,
    accountId?: string,
  ) {
    try {
      // Create a default pagination object with no limits
      const pageOptionsDto = {
        page: 1,
        limit: Number.MAX_SAFE_INTEGER, // Get all records
        paginate: false, // Indicate we don't want pagination
        skip: 0,
      };

      // Only pass marketCenterId if it exists, otherwise use accountId
      const effectiveMarketCenterId =
        marketCenterId && marketCenterId !== '' && marketCenterId !== undefined
          ? marketCenterId
          : undefined;
      const effectiveAccountId =
        !effectiveMarketCenterId && accountId && accountId !== ''
          ? accountId
          : undefined;

      const { data: agents } = await this.agentRepository.findAllAgents(
        pageOptionsDto,
        sortSearchDataDto,
        {
          ...filterAgentDto,
          // Convert string 'true'/'false' to boolean if needed
          isActive: filterAgentDto.isActive
            ? filterAgentDto.isActive.toString()
            : undefined,
          tinNumber: filterAgentDto.tinNumber,
          licenseNumber: filterAgentDto.licenseNumber,
          zipCode: filterAgentDto.zipCode,
          city: filterAgentDto.city,
          state: filterAgentDto.state,
          startDate: filterAgentDto.startDate,
          endDate: filterAgentDto.endDate,
          ids: filterAgentDto.ids,
        },
        effectiveMarketCenterId, // Only pass marketCenterId if it exists
        effectiveAccountId, // Only pass accountId if marketCenterId doesn't exist
      );

      if (!agents.length) {
        throw new BadRequestException('No data available to export');
      }

      // Transform the data for Excel export
      const excelData = agents.map((agent, index) => ({
        'Sr. No': index + 1,
        'First Name': agent.user.firstName,
        'Last Name': `${agent.user.lastName}`,
        'Email id': agent.user.email,
        'Phone Number': agent.user.contactNumber,
        'License Number': agent.license_number || 'N/A',
        Status: agent.isActive ? 'Active' : 'Inactive',
        'Date Added': agent.createdAt
          ? new Date(agent.createdAt).toLocaleDateString()
          : 'N/A',
        'Service Provider Associated': 0,
        'Properties Associated': 0,
      }));

      // Generate and send Excel file
      await this.excelService.generateExcelSheet({
        data: excelData,
        response,
        fileName: 'Agent_List',
      });
    } catch (error) {
      this.logger.error('Error in downloadAgentReport:', error);
      throw error;
    }
  }

  async softDeleteAgent(id: Identifier): Promise<AgentDetail> {
    const result = await this.agentRepository.softDeleteAgent(id);
    return result;
  }

  async getAllExperienceOptions(): Promise<{
    data: AgentExperienceOption[];
    message: string;
  }> {
    try {
      const experienceOptions = await this.agentRepository.findAllExperienceOptions();

      return {
        data: experienceOptions,
        message: 'Agent experience options retrieved successfully!',
      };
    } catch (error) {
      this.logger.error(`Error in getAllExperienceOptions: ${error.message}`);
      throw error;
    }
  }
}
