import React from 'react';
import { cn } from '@vast-application-framework/ui-components';

interface cardProps {
  title?: string;
  amount?: string;
  text?: string;
  textColor?: string;
  icon?: React.ReactNode;
  containerClassName?: string;
  titleClassName?: string;
  amountClassName?: string;
  textClassName?: string;
  iconClassName?: string;
}

const Card: React.FC<cardProps> = ({
  title,
  amount,
  text,
  textColor,
  icon,
  containerClassName,
  titleClassName,
  amountClassName,
  textClassName,
  iconClassName,
}) => {
  return (
    <div
      className={cn(
        'relative bg-hw-white rounded-xl p-2 border max-w-[500px] overflow-hidden',
        containerClassName,
      )}
    >
      {/* SVG Wave background */}
      <svg
        className="absolute top-0 left-0 w-full h-full z-0"
        viewBox="0 0 325 92"
        preserveAspectRatio="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M0 0H13.5417C27.0833 0 54.1667 0 81.25 11.9259C108.333 23.8519 135.417 47.7037 162.5 57.9259C189.583 68.1481 216.667 64.7407 243.75 54.5185C270.833 44.2963 297.917 27.2593 311.458 18.7407L325 10.2222V92H311.458C297.917 92 270.833 92 243.75 92C216.667 92 189.583 92 162.5 92C135.417 92 108.333 92 81.25 92C54.1667 92 27.0833 92 13.5417 92H0V0Z"
          fill="rgba(0, 0, 0, 0.05)"
        />
      </svg>

      {/* Content Layer */}
      <div className="relative z-10">
        <div className="flex items-center gap-2 mb-1">
          {icon && (
            <div
              className={cn(
                'flex-shrink-0 text-sm text-black font-normal',
                iconClassName,
              )}
            >
              {icon}
            </div>
          )}
          <p className={cn('text-sm text-black font-normal', titleClassName)}>
            {title}
          </p>
        </div>
        <div>
          <p
            className={cn('font-semibold text-2xl text-black', amountClassName)}
          >
            {amount}
          </p>
          <span className={cn('text-xs font-medium', textColor, textClassName)}>
            {text}
          </span>
        </div>
      </div>
    </div>
  );
};

export default Card;
