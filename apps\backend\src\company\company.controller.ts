import {
  Controller,
  Post,
  Body,
  Req,
  Get,
  Param,
  Query,
  Patch,
  UseInterceptors,
  ParseFilePipeBuilder,
  HttpStatus,
  BadRequestException,
  Logger,
  Res,
  Delete,
  HttpException,
  UploadedFiles,
} from '@nestjs/common';
import { CompanyService } from './company.service';
import { CreateCompanyDto } from './dto/create-company.dto';
import { AccountDetail } from './entities/account-detail.entity';
import { UserType } from '@shared-types';
import { PLATFORM_ENDPOINT } from '../common/constant/apiEndPoints';
import { PageOptionsDto } from '../common/pagination/pageOptions.dto';
import { SortSearchDataDto } from '../common/pagination/sort-search-data.dto';
import { CompanyFilterDto } from './dto/company-filter.dto';
import { PageDto } from '../common/pagination/page.dto';
import { PermissionCheck } from '../common/decorators';
import { ApiBody, ApiConsumes, ApiOperation } from '@nestjs/swagger';
import { CompanyType } from './entities/company_type.entity';
import { ActivateDeactivateCompanyDto } from './dto/activate-deactivate-company.dto';
import { FilesInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import { MinioService } from '../common/minio/minio.service';

@Controller(`${PLATFORM_ENDPOINT}company`)
export class CompanyController {
  constructor(
    private readonly companyService: CompanyService,
    private readonly minioService: MinioService,
  ) {}
  @Post()
  @PermissionCheck({ isPlatformAdmin: true })
  @ApiOperation({ summary: 'Create Company' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(
    FilesInterceptor('files', 5, {
      limits: {
        fileSize: 5 * 1024 * 1024, // 5MB
      },
    }),
  )
  @ApiBody({ type: CreateCompanyDto })
  async createCompany(
    @Body() body: any,
    @Req() request: { user: UserType },
    @UploadedFiles(
      new ParseFilePipeBuilder()
        .addFileTypeValidator({
          fileType: /(jpg|jpeg|png|gif|pdf)$/, // Added PDF support
        })
        .addMaxSizeValidator({
          maxSize: 5 * 1024 * 1024, // 5MB
        })
        .build({
          errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
          fileIsRequired: false,
        }),
    )
    files?: Express.Multer.File[],
  ): Promise<{ data: any; message: string }> {
    const createCompanyDto = {
      company: JSON.parse(body.company),
      primaryAddress: JSON.parse(body.primaryAddress),
      mailingAddress: JSON.parse(body.mailingAddress),
      admins: body.admins ? JSON.parse(body.admins) : [],
      isBothAddressSame: body.isBothAddressSame === 'true',
    };

    const { user } = request;
    return this.companyService.createCompany(createCompanyDto, user, files);
  }
  
  @Get('legalentities')
  async getAllEntities(): Promise<{ data: CompanyType[] }> {
    return await this.companyService.findAllLegalEntities();
  }

  @Get('downloadreport')
  async downloadReport(
    @Query() sortSearch: SortSearchDataDto,
    @Query() filters: CompanyFilterDto,
    @Res() response: Response,
  ) {
    return await this.companyService.downloadCompanyReport(
      sortSearch,
      filters,
      response,
    );
  }
  // Get all companies
  @Get()
  async findCompanies(
    @Query() pageOptions: PageOptionsDto,
    @Query() sortSearch: SortSearchDataDto,
    @Query() filters: CompanyFilterDto,
  ): Promise<PageDto<AccountDetail>> {
    return await this.companyService.findCompanies(
      pageOptions,
      sortSearch,
      filters,
    );
  }

  // 🔹 Get company by company ID
  @Get(':id')
  async findCompanyById(
    @Param('id') id: string,
  ): Promise<AccountDetail | null> {
    return this.companyService.findCompanyById(id);
  }

  @Patch(':id')
  @PermissionCheck({ isPlatformAdmin: true })
  @ApiOperation({ summary: 'Update Company' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(
    FilesInterceptor('files', 5, {
      limits: {
        fileSize: 5 * 1024 * 1024, // 5MB
      },
    }),
  )
  @ApiBody({ type: CreateCompanyDto, required: false }) // Using CreateCompanyDto as reference
  async updateCompany(
    @Param('id') id: string,
    @Body() body: any,
    @Req() request: { user: UserType },
    @UploadedFiles(
      new ParseFilePipeBuilder()
        .addFileTypeValidator({
          fileType: /(jpg|jpeg|png|gif|pdf)$/, // Added PDF support
        })
        .addMaxSizeValidator({
          maxSize: 5 * 1024 * 1024, // 5MB
        })
        .build({
          errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
          fileIsRequired: false,
        }),
    )
    files?: Express.Multer.File[],
  ): Promise<{ data: any; message: string }> {
    try {
      //code may needed for parsing

      const updateCompanyDto = {
        ...(body.company && { company: JSON.parse(body.company) }),
        ...(body.primaryAddress && {
          primaryAddress: JSON.parse(body.primaryAddress),
        }),
        ...(body.mailingAddress && {
          mailingAddress: JSON.parse(body.mailingAddress),
        }),
        ...(body.admins && { admins: JSON.parse(body.admins) }),
        ...(body.isBothAddressSame && {
          isBothAddressSame: body.isBothAddressSame === 'true',
        }),
      };

      return await this.companyService.updateCompany(
        id,
        updateCompanyDto,
        files,
      );
    } catch (error) {
      Logger.log('error while upadating the', error);
      throw new BadRequestException(error.message || 'Error in update company');
    }
  }
  @Patch('changestatus/:id')
  @PermissionCheck({ isPlatformAdmin: true })
  @ApiOperation({ summary: 'Change company status (activate/deactivate)' })
  async changeCompanyStatus(
    @Param('id') id: string,
    @Body() activateDeactivateCompanyDto: ActivateDeactivateCompanyDto,
  ): Promise<{ data: AccountDetail; message: string }> {
    const company = await this.companyService.changeCompanyStatus(
      id,
      activateDeactivateCompanyDto,
    );
    return {
      data: company,
      message: `Company ${
        activateDeactivateCompanyDto.isActive ? 'activated' : 'deactivated'
      } successfully`,
    };
  }

  @Delete('/:id')
  async deleteCompany(@Param('id') id: string) {
    const deleted = await this.companyService.deleteCompany(id);
    if (!deleted) {
      throw new HttpException('Company not found', HttpStatus.NOT_FOUND);
    }
    return { message: 'Company deleted successfully' };
  }

  //   @Get('/company/dashboard')
  // async getDashboard(
  //   @Query('companyId') companyId: string,
  //   @Query('dateRange') dateRange: string,
  // ) {
  //   return this.companyService.getDashboard(companyId, dateRange);
  // }
}
