{"name": "@vast-application-framework/source", "version": "0.0.0", "license": "MIT", "lint-staged": {"apps/**/*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write --ignore-unknown"]}, "scripts": {"find-deadcode": "ts-prune", "build:backend": "nx build backend && tsc -p apps/backend/tsconfig.build.json", "backend": "nx serve backend", "frontend": "nx serve frontend", "hw": "nx serve homeworks-web", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "migration:backend:run": "node ./node_modules/typeorm/cli.js migration:run --dataSource ./dist/apps/backend/src/datasource.js", "migration:backend:revert": "node ./node_modules/typeorm/cli.js migration:revert --dataSource ./dist/apps/backend/src/datasource.js", "seed": "node ./dist/apps/backend/src/seeder/script.js", "prepare": "husky install", "format": "prettier apps --write --ignore-unknown", "lint:backend": "eslint './apps/backend/**/*.{js,jsx,ts,tsx}'", "lint:frontend": "eslint './apps/frontend/**/*.{js,jsx,ts,tsx}'", "lint:report-backend": "eslint './apps/backend/**/*.{js,jsx,ts,tsx}' --format html --output-file ./eslint-report-backend.html", "lint:report-frontend": "eslint './apps/frontend/**/*.{js,jsx,ts,tsx}' --format html --output-file ./eslint-report-frontend.html"}, "private": true, "dependencies": {"@hookform/resolvers": "^3.10.0", "@nestjs/bull": "^11.0.2", "@nestjs/cache-manager": "^2.2.2", "@nestjs/common": "^10.0.2", "@nestjs/config": "^3.0.1", "@nestjs/core": "^10.0.2", "@nestjs/event-emitter": "^2.0.4", "@nestjs/jwt": "^10.1.0", "@nestjs/mapped-types": "*", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.3.7", "@nestjs/platform-socket.io": "^10.4.18", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^7.4.0", "@nestjs/typeorm": "^10.0.0", "@nestjs/websockets": "^10.4.18", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-scroll-area": "^1.0.4", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@sendgrid/mail": "^7.7.0", "@socket.io/redis-adapter": "^8.3.0", "@tanstack/react-query": "^5.66.11", "@tanstack/react-table": "^8.9.3", "axios": "^1.7.9", "axios-auth-refresh": "^3.3.6", "bcrypt": "^5.1.0", "cache-manager": "^5.7.4", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "class-variance-authority": "^0.7.0", "cloudinary": "^1.41.0", "clsx": "^2.0.0", "cron": "^4.3.1", "csv-parse": "^5.5.5", "date-fns": "^4.1.0", "dayjs": "^1.11.12", "encoding": "^0.1.13", "exceljs": "^4.4.0", "firebase-admin": "^13.4.0", "i18next": "^23.12.2", "i18next-http-backend": "^2.5.2", "input-otp": "^1.4.2", "js-base64": "^3.7.7", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lucide-react": "^0.475.0", "minio": "^8.0.5", "mjml": "^4.15.3", "multer": "^1.4.5-lts.1", "next-themes": "^0.4.4", "passport": "^0.6.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "passport-oauth2": "^1.8.0", "pg": "^8.11.1", "react": "18.2.0", "react-csv": "^2.2.2", "react-dom": "18.2.0", "react-hook-form": "^7.54.2", "react-i18next": "^15.0.0", "react-phone-input-2": "^2.15.1", "react-router-dom": "6.11.2", "react-select": "^5.8.0", "react-tailwindcss-datepicker": "^1.7.3", "react-textarea-autosize": "^8.5.8", "react-toastify": "^9.1.3", "recharts": "^3.0.0", "redis": "^4.7.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0", "shadcn": "^2.4.0-canary.9", "socket.io": "^4.8.1", "sonner": "^1.7.4", "streamifier": "^0.1.1", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.6", "tslib": "^2.3.0", "typeorm": "^0.3.17", "uuid": "^11.0.5", "vite-plugin-svgr": "^4.3.0", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.1", "@nestjs/testing": "^10.0.2", "@nx/cypress": "17.0.0", "@nx/eslint": "17.0.0", "@nx/eslint-plugin": "17.0.0", "@nx/jest": "17.0.0", "@nx/js": "17.0.0", "@nx/nest": "17.0.0", "@nx/node": "17.0.0", "@nx/react": "17.0.0", "@nx/rollup": "17.0.0", "@nx/vite": "17.0.0", "@nx/web": "17.0.0", "@nx/webpack": "17.0.0", "@nx/workspace": "17.0.0", "@rollup/plugin-url": "^7.0.0", "@svgr/rollup": "^8.0.1", "@swc-node/register": "~1.6.7", "@swc/cli": "~0.1.62", "@swc/core": "~1.3.85", "@swc/helpers": "~0.5.2", "@testing-library/react": "14.0.0", "@types/bcrypt": "^5.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.4.0", "@types/js-cookie": "^3.0.6", "@types/mjml": "^4.7.4", "@types/multer": "^1.4.12", "@types/node": "18.14.2", "@types/passport-jwt": "^3.0.10", "@types/passport-local": "^1.0.36", "@types/passport-oauth2": "^1.4.17", "@types/react": "^18.2.24", "@types/react-csv": "^1.1.10", "@types/react-dom": "18.2.9", "@types/supertest": "^2.0.12", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "5.62.0", "@typescript-eslint/parser": "5.62.0", "@vitejs/plugin-react": "~4.0.0", "@vitest/coverage-c8": "~0.32.0", "@vitest/ui": "~0.32.0", "autoprefixer": "^10.4.15", "babel-jest": "^29.4.1", "core-js": "^3.6.5", "cypress": "^13.0.0", "dotenv-cli": "^7.4.2", "eslint": "8.46.0", "eslint-config-next": "13.4.1", "eslint-config-prettier": "9.1.0", "eslint-formatter-html": "^2.7.2", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-cypress": "2.15.2", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^8.0.3", "jest": "^29.4.1", "jest-environment-jsdom": "^29.4.1", "jest-environment-node": "^29.4.1", "jsdom": "~22.1.0", "lint-staged": "^15.2.9", "nx": "^17.0.0", "postcss": "^8.4.28", "prettier": "^2.8.8", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "swc-loader": "0.1.15", "tailwindcss": "^3.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-migrate": "^0.1.35", "ts-node": "10.9.1", "ts-prune": "^0.10.3", "tsconfig-paths": "^4.2.0", "typescript": "~5.1.3", "vite": "~4.3.9", "vitest": "~0.32.0"}}