import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { PropertyAsset } from './entities/property-asset.entity';
import { extractFcmTokens } from '../service-requests/utils';
import { PushNotificationService } from '../common/push-notifications/push-notification.service';
import { NOTIFICATION_TEMPLATES } from '../common/push-notifications/enum/notification-templates.enum';
import { NOTIFICATION_TYPE } from '../notifications/entities/notification.entity';
import dayjs from 'dayjs';
import { CronJob } from '@nestjs/schedule/node_modules/cron';
import { SchedulerRegistry } from '@nestjs/schedule';
import { startOfToday, addDays } from 'date-fns';
import { ConfigService } from '@nestjs/config';
@Injectable()
export class PropertyAssetReminderService implements OnModuleInit {
  private readonly logger = new Logger(PropertyAssetReminderService.name);
  constructor(
    private readonly dataSource: DataSource,
    private readonly pushNotificationService: PushNotificationService,
    private schedulerRegistry: SchedulerRegistry,
    private configService: ConfigService,
  ) {}

  onModuleInit() {
    this.initiateAMCSchedular();
    this.initiateWarrantySchedular();
  }

  initiateAMCSchedular() {
    const cronTime =
      this.configService.get<string>('AMC_REMINDER_CRON') ||
      process.env.AMC_REMINDER_CRON;

    if (!cronTime) {
      this.logger.warn('AMC_REMINDER_CRON is not set. Skipping job.');
      return;
    }

    const job = new CronJob(cronTime, () => {
      this.logger.log('Running scheduled tasks...');
      this.handlePropertyAssetAMCExpiryReminder();
    });

    this.schedulerRegistry.addCronJob('amcReminderJob', job);
    this.logger.log(`amcReminderJob with schedular ${cronTime} added`);
    job.start();
  }
  initiateWarrantySchedular() {
    const cronTime =
      this.configService.get<string>('WARRANTY_REMINDER_CRON') ||
      process.env.WARRANTY_REMINDER_CRON;

    if (!cronTime) {
      this.logger.warn('WARRANTY_REMINDER_CRON is not set. Skipping job.');
      return;
    }

    const job = new CronJob(cronTime, () => {
      this.logger.log('Running scheduled tasks...');

      this.handlePropertyAssetWarrantyExpiryReminder();
    });

    this.schedulerRegistry.addCronJob('warrantyReminderJob', job);
    this.logger.log(`warrantyReminderJob with schedular ${cronTime} added`);
    job.start();
  }

  async sendWarrantyReminder(propertyAsset: PropertyAsset) {
    const ownerId = propertyAsset?.propertyOwner?.owner?.id;

    const fcmTokens = extractFcmTokens(propertyAsset?.propertyOwner?.owner);
    this.pushNotificationService.sendPushNotification(
      NOTIFICATION_TEMPLATES.PROPERTY_ASSET_WARRANTY_EXPIRY_REMINDER,
      fcmTokens,
      {
        receiverId: ownerId,
        userId: ownerId,
        endDate: dayjs(propertyAsset.warrantyEndDate).format('DD MMM YYYY'),
        assetName: propertyAsset.name,
        data: {
          receiverId: ownerId,
          userId: ownerId,
          type: NOTIFICATION_TYPE.ASSET_WARRANTY,
          propertyAssetId: propertyAsset.id,
          propertyId: propertyAsset?.propertyOwner?.propertyId,
          propertyOwnerId: propertyAsset?.propertyOwner?.id,
          endDate: dayjs(propertyAsset.warrantyEndDate).format('DD MMM YYYY'),
          assetName: propertyAsset.name,
        },
      },
    );
  }
  async sendAMCExpiryReminder(propertyAsset: PropertyAsset) {
    const ownerId = propertyAsset?.propertyOwner?.owner?.id;

    const fcmTokens = extractFcmTokens(propertyAsset?.propertyOwner?.owner);
    this.pushNotificationService.sendPushNotification(
      NOTIFICATION_TEMPLATES.PROPERTY_ASSET_AMC_EXPIRY_REMINDER,
      fcmTokens,
      {
        receiverId: ownerId,
        userId: ownerId,
        endDate: dayjs(propertyAsset.amcEndDate).format('DD MMM YYYY'),
        assetName: propertyAsset.name,
        data: {
          receiverId: ownerId,
          userId: ownerId,
          type: NOTIFICATION_TYPE.ASSET_AMC,
          propertyAssetId: propertyAsset.id,
          propertyId: propertyAsset?.propertyOwner?.propertyId,
          propertyOwnerId: propertyAsset?.propertyOwner?.id,
          endDate: dayjs(propertyAsset.amcEndDate).format('DD MMM YYYY'),
          assetName: propertyAsset.name,
        },
      },
    );
  }

  async sendWarrantyRemindersToOwner() {
    const dateMinus7 = addDays(startOfToday(), +7);
    const dateMinus1 = addDays(startOfToday(), +1);

    const propertyAssets = await this.dataSource
      .createQueryBuilder(PropertyAsset, 'pa')
      .where('pa.warrantyEndDate IN (:...dates)', {
        dates: [dateMinus7, dateMinus1],
      })
      .leftJoinAndSelect(
        'pa.propertyOwner',
        'propertyOwner',
        'propertyOwner.isActive = :isActive AND propertyOwner.isDeleted = :isDeleted',
        { isActive: true, isDeleted: false },
      )
      .leftJoinAndSelect(
        'propertyOwner.owner',
        'owner',
        'owner.isActive = :isActive AND owner.isDeleted = :isDeleted',
        { isActive: true, isDeleted: false },
      )
      .select([
        'pa',
        'propertyOwner.id',
        'propertyOwner.propertyId',
        'propertyOwner.ownerId',
        'owner.id',
        'owner.fcmTokens',
      ])
      .getMany();

    if (propertyAssets?.length) {
      Promise.all(
        propertyAssets.map((propertyAsset) =>
          this.sendWarrantyReminder(propertyAsset),
        ),
      );
    }
  }

  async sendAMCExpiryNotificationWithAddedReminders() {
    const today = startOfToday();

    const propertyAssets = await this.dataSource
      .createQueryBuilder(PropertyAsset, 'pa')
      .innerJoinAndSelect(
        'pa.amcReminders',
        'amcReminders',
        'amcReminders.reminderDate = :today',
        { today },
      )
      .leftJoinAndSelect(
        'pa.propertyOwner',
        'propertyOwner',
        'propertyOwner.isActive = :isActive AND propertyOwner.isDeleted = :isDeleted',
        { isActive: true, isDeleted: false },
      )
      .leftJoinAndSelect(
        'propertyOwner.owner',
        'owner',
        'owner.isActive = :isActive AND owner.isDeleted = :isDeleted',
        { isActive: true, isDeleted: false },
      )
      .select([
        'pa',
        'amcReminders',
        'propertyOwner.id',
        'propertyOwner.propertyId',
        'propertyOwner.ownerId',
        'owner.id',
        'owner.fcmTokens',
      ])
      .getMany();

    if (propertyAssets?.length) {
      Promise.all(
        propertyAssets.map((propertyAsset) =>
          this.sendAMCExpiryReminder(propertyAsset),
        ),
      );
    }
  }
  async sendAMCExpiryNotificationWithoutAddedReminders() {
    const dateMinus7 = addDays(startOfToday(), +7);
    const dateMinus1 = addDays(startOfToday(), +1);

    const propertyAssets = await this.dataSource
      .createQueryBuilder(PropertyAsset, 'pa')
      .leftJoinAndSelect('pa.amcReminders', 'amcReminders')
      .leftJoinAndSelect(
        'pa.propertyOwner',
        'propertyOwner',
        'propertyOwner.isActive = :isActive AND propertyOwner.isDeleted = :isDeleted',
        { isActive: true, isDeleted: false },
      )
      .leftJoinAndSelect(
        'propertyOwner.owner',
        'owner',
        'owner.isActive = :isActive AND owner.isDeleted = :isDeleted',
        { isActive: true, isDeleted: false },
      )
      .where('amcReminders.id IS NULL') // no reminders
      .andWhere('pa.amcEndDate IN (:...dates)', {
        dates: [dateMinus7, dateMinus1],
      })
      .select([
        'pa',
        'propertyOwner.id',
        'propertyOwner.propertyId',
        'propertyOwner.ownerId',
        'owner.id',
        'owner.fcmTokens',
      ])
      .getMany();

    if (propertyAssets?.length) {
      Promise.all(
        propertyAssets.map((propertyAsset) =>
          this.sendAMCExpiryReminder(propertyAsset),
        ),
      );
    }
  }

  handlePropertyAssetWarrantyExpiryReminder() {
    this.sendWarrantyRemindersToOwner();
  }

  handlePropertyAssetAMCExpiryReminder() {
    this.sendAMCExpiryNotificationWithAddedReminders();
    this.sendAMCExpiryNotificationWithoutAddedReminders();
  }
}
