import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { Between, DataSource, Repository } from 'typeorm';
import { AccountDetail } from '../entities/account-detail.entity';
import { AccountUser } from '../../accountusers/entities/account.user.entity';
import { UserRole } from '../../role/entities/user-role.entity';
import { User } from '../../users/entities/user.entity';
import { Address } from '../../users/entities/address.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { MarketCenterUser } from '../../market-centers/entities/market-center-user.entity';
import { MarketCenter } from '../../market-centers/entities/market-center.entity';
import { ActivateDeactivateCompanyDto } from '../dto/activate-deactivate-company.dto';
import { Account } from '../../accounts/entities/account.entity';
import { AccountLegalDocument } from '../../document/entities/account-legaldocument.entity';
import { MinioService } from '../../common/minio/minio.service';
import { MarketCentersRepository } from '../../market-centers/repositories/market-centers.repository';
@Injectable()
export class CompanyRepository {
  private readonly companyRepo: Repository<AccountDetail>;
  private minioService: MinioService;
  @InjectRepository(Address)
  private readonly addressRepository: Repository<Address>;
  @InjectRepository(Account)
  private readonly accountRepository: Repository<Account>;
  @InjectRepository(AccountLegalDocument)
  private readonly accountLegalDocumentRepository: Repository<AccountLegalDocument>;
  // private readonly marketCenterRepository : MarketCentersRepository;
  // private readonly marketCenterRepository : MarketCentersRepository


  constructor(private readonly dataSource: DataSource) {
    this.companyRepo = this.dataSource.getRepository(AccountDetail);
  }

  // 🔹 Get company by company ID
  async findCompanyById(accountId: string): Promise<any> {
    const company = await this.accountRepository
      .createQueryBuilder('account')
      .leftJoinAndSelect('account.accountDetail', 'company')
      .leftJoinAndSelect('company.primaryAddress', 'primaryAddress')
      .leftJoinAndSelect('company.mailingAddress', 'mailingAddress')
      .leftJoinAndSelect('company.companyType', 'companyType')
      .leftJoinAndSelect(
        'account.accountUsers',
        'accountUsers',
        'accountUsers.isDeleted = false',
      )
      .leftJoinAndSelect('accountUsers.userRole', 'userRole')
      .leftJoinAndSelect('userRole.role', 'role')
      .leftJoinAndSelect('userRole.user', 'user')
      .where('account.id = :accountId', { accountId })
      .getOne();

    if (!company) {
      throw new NotFoundException(
        `Company with account ID ${accountId} not found`,
      );
    }

    // Get legal documents using the accountDetail id
    const docs = await this.getLegalDocuments([company.accountDetail.id]);

    const transformedCompany = {
      id: company.id, // account id
      code: company.code,
      name: company.name,
      type: company.accountDetail.companyType?.name,
      primaryAddress: company.accountDetail.primaryAddress,
      mailingAddress: company.accountDetail.mailingAddress,
      isActive: company.isActive,
      admins:
        company.accountUsers?.map((accountUser) => {
          // Basic admin information - roles will be added by the service

          return {
            id: accountUser.userRole?.user.id,
            firstName: accountUser.userRole?.user.firstName,
            lastName: accountUser.userRole?.user.lastName,
            username: accountUser.userRole?.user.username,
            emailId: accountUser.userRole?.user.email,
            contactNumber: accountUser.userRole?.user.contactNumber,
            isActive: accountUser.userRole?.user.isActive,
            // These will be populated by the service
            hasMultipleRoles: false,
            roles: [],
          };
        }) || [],
      legalDocUrl: docs || [],
      accountId: company.id,
      // Spread remaining company properties that we want to keep
      ...Object.fromEntries(
        Object.entries(company.accountDetail).filter(
          ([key]) =>
            ![
              'id',
              'name',
              'primaryAddress',
              'mailingAddress',
              'companyType',
              'isActive',
            ].includes(key),
        ),
      ),
    };

    return transformedCompany;
  }
  // 🔹 Create new company
  async createCompany(
    companyData: Partial<AccountDetail>,
  ): Promise<AccountDetail> {
    const newCompany = this.companyRepo.create(companyData);
    await this.companyRepo.save(newCompany);
    return newCompany;
  }

  async findOne(query: object): Promise<AccountDetail | null> {
    if (!query || Object.keys(query).length === 0) {
      throw new Error('Invalid query: You must provide selection conditions.');
    }

    const company = await this.companyRepo.findOne({
      where: { ...query },
      relations: ['primaryAddress', 'mailingAddress', 'companyType', 'account'],
    });
    return company;
  }

  private async getLegalDocuments(accountDetailIds: string[]) {
    return this.dataSource
      .createQueryBuilder()
      .select([
        'ald.id as account_doc_id',
        'd.id as id',
        'd.original_filename as original_filename',
        'd.minio_filename as minio_filename',
        'd.type as type',
      ])
      .from(AccountLegalDocument, 'ald')
      .innerJoin('document', 'd', 'd.id = ald.document_id')
      .where('ald.account_detail_id IN (:...accountDetailIds)', {
        accountDetailIds,
      })
      .getRawMany();
  }

  async findCompaniesWithFilters(
    ids?: string[],
    search?: string,
    isActive?: string,
    companyName?: string,
    type?: string,
    zipcode?: string[],
    city?: string[],
    state?: string[],
    startDate?: string,
    endDate?: string,
    sortBy = 'name',
    order: 'ASC' | 'DESC' = 'ASC',
    limit?: number,
    page?: number,
  ) {
    const queryBuilder = this.accountRepository
      .createQueryBuilder('account')
      .addSelect('LOWER(account.name)', 'account_name_lower')
      .leftJoinAndSelect('account.accountDetail', 'company')
      .leftJoinAndSelect('company.primaryAddress', 'primaryAddress')
      .leftJoinAndSelect('company.mailingAddress', 'mailingAddress')
      .leftJoinAndSelect('company.companyType', 'company_type')
      .leftJoinAndSelect(
        'account.accountUsers',
        'accountUsers',
        'accountUsers.isDeleted = false',
      )
      .leftJoinAndSelect('accountUsers.userRole', 'userRole')
      .leftJoinAndSelect('userRole.role', 'role')
      .leftJoinAndSelect('userRole.user', 'user')
      .leftJoinAndSelect(
        'account.marketCenters',
        'marketCenters',
        'marketCenters.isDeleted = false',
      )
      .loadRelationCountAndMap(
        'company.marketCentersCount',
        'account.marketCenters',
        'marketCenters',
        (qb) => qb.where('marketCenters.isDeleted = false'),
      )
      .where('account.isDeleted = :isDeleted', { isDeleted: false })
      .andWhere('role.name = :roleName', { roleName: 'company_admin' });

    // Get total count before applying filters, excluding PLATFORM accounts
    const totalCompanies = await this.accountRepository
      .createQueryBuilder('account')
      .where('account.isDeleted = :isDeleted', { isDeleted: false })
      .andWhere('account.type = :type', { type: 'BUSINESS' }) // Only count BUSINESS type accounts
      .getCount();
    if (ids?.length) {
      queryBuilder.andWhere('account.id IN (:...ids)', { ids });
    }

    if (search?.trim() || companyName?.trim()) {
      queryBuilder.andWhere('account.name ILIKE :filterValue', {
        filterValue: `%${search || companyName}%`,
      });
    }
    if (type?.trim()) {
      const trimmedType = type.trim().replace(/\s+/g, ' '); // Replace multiple spaces with single space
      queryBuilder.andWhere(
        'TRIM(LOWER(company_type.name)) = TRIM(LOWER(:type))',
        {
          type: trimmedType,
        },
      );
    }
    if (startDate?.trim() && !isNaN(Date.parse(startDate))) {
      const parsedStartDate = new Date(startDate);
      queryBuilder.andWhere('account.createdAt >= :startDate', {
        startDate: parsedStartDate.toISOString(),
      });
    }

    if (endDate?.trim() && !isNaN(Date.parse(endDate))) {
      const parsedEndDate = new Date(endDate);
      queryBuilder.andWhere('account.createdAt <= :endDate', {
        endDate: parsedEndDate.toISOString(),
      });
    }

    if (isActive?.trim()) {
      const status = isActive.toLowerCase() === 'true';
      queryBuilder.andWhere('account.isActive = :status', { status });
    }

    if (zipcode?.length) {
      queryBuilder.andWhere(
        '(primaryAddress.zipcode IN (:...zipcode) OR mailingAddress.zipcode IN (:...zipcode))',
        { zipcode },
      );
    }

    if (city?.length) {
      queryBuilder.andWhere(
        '(primaryAddress.city IN (:...city) OR mailingAddress.city IN (:...city))',
        { city },
      );
    }

    if (state?.length) {
      queryBuilder.andWhere(
        '(primaryAddress.state IN (:...state) OR mailingAddress.state IN (:...state))',
        { state },
      );
    }

    // Handle sorting based on different fields
    try {
      if (sortBy === 'state') {
        queryBuilder.orderBy('primaryAddress.state', order);
      } else if (sortBy === 'city') {
        queryBuilder.orderBy('primaryAddress.city', order);
      } else if (sortBy === 'type') {
        queryBuilder.orderBy('company_type.name', order, 'NULLS LAST');
      } else if (sortBy === 'name') {
        queryBuilder.orderBy('account_name_lower', order as 'ASC' | 'DESC');
      } else if (sortBy === 'code') {
        queryBuilder.orderBy('account.code', order);
      } else if (sortBy === 'createdAt' || sortBy === 'updatedAt') {
        queryBuilder.orderBy(`account.${sortBy}`, order);
      } else if (sortBy === 'isActive') {
        queryBuilder.orderBy('account.isActive', order);
      } else if (
        sortBy === 'emailId' ||
        sortBy === 'phoneNumber' ||
        sortBy === 'businessRegistrationNumber' ||
        sortBy === 'taxIdNumber' ||
        sortBy === 'website' ||
        sortBy === 'licenseNumber'
      ) {
        queryBuilder.orderBy(`company.${sortBy}`, order, 'NULLS LAST');
      } else {
        // Default to sorting by account name if the sortBy field is not recognized
        queryBuilder.orderBy('account.name', order, 'NULLS LAST');
      }
    } catch (error) {
      // If there's an error with sorting, default to account name
      Logger.error(`Error in sorting: ${error.message}`);
      queryBuilder.orderBy('account.name', order, 'NULLS LAST');
    }
    queryBuilder.distinct(true);
    // queryBuilder.addOrderBy('accountUsers.createdAt', 'ASC');

    if (limit && page) {
      queryBuilder.skip((page - 1) * limit).take(limit);
    }

    const [data, filteredTotal] = await queryBuilder.getManyAndCount();

    if (data && data.length > 0) {
      const formattedData = data.map((account) => {
        const company = account.accountDetail;
        return {
          id: account.id,
          name: account.name,
          type: company.companyType?.name || '',
          primaryAddress: company.primaryAddress,
          mailingAddress: company.mailingAddress,
          createdAt: account.createdAt,
          updatedAt: account.updatedAt,
          isActive: account.isActive,
          code: account.code,
          businessRegistrationNumber: company.businessRegistrationNumber,
          taxIdNumber: company.taxIdNumber,
          website: company.website,
          licenseNumber: company.licenseNumber,
          phoneNumber: company.phoneNumber,
          emailId: company.emailId,

          admins:
            account.accountUsers
              ?.map((accountUser) => ({
                id: accountUser.userRole.user.id,
                firstName: accountUser.userRole.user.firstName,
                lastName: accountUser.userRole.user.lastName,
                username: accountUser.userRole.user.username,
                emailId: accountUser.userRole.user.email,
                contactNumber: accountUser.userRole.user.contactNumber,
                isActive: accountUser.userRole.user.isActive,
              }))
              .filter(Boolean) || [],
          marketCenters:
            account.marketCenters?.map((mc) => ({
              id: mc.id,
              name: mc.name,
              code: mc.code,
              isActive: mc.isActive,
              isDeleted: mc.isDeleted,
              phoneNumber: mc.phoneNumber,
              createdAt: mc.createdAt,
              updatedAt: mc.updatedAt,
            })) || [],
          marketCentersCount: account['marketCentersCount'] || 0,
        };
      });

      return {
        data: formattedData,
        total: filteredTotal,
        totalCompanies,
      };
    }

    return { data: [], total: 0, totalCompanies };
  }

  async updateCompany(
    id: string,
    updateCompanyDto: Partial<AccountDetail>,
  ): Promise<AccountDetail> {
    const existingCompany = await this.findCompanyById(id);

    if (!existingCompany) {
      throw new NotFoundException(`Company with ID ${id} not found`);
    }

    // Merge only updated fields
    await this.companyRepo.update(id, updateCompanyDto);

    return await this.findCompanyById(id);
  }

  async changeCompanyStatus(
    id: string,
    activateDeactivateCompanyDto: ActivateDeactivateCompanyDto,
  ): Promise<AccountDetail> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const existingCompany = await this.findCompanyById(id);
      if (!existingCompany) {
        throw new NotFoundException(`Company with ID ${id} not found`);
      }

      // Update account status
      const existingAccount = await queryRunner.manager.findOne(Account, {
        where: { id: id, isDeleted: false },
      });

      if (!existingAccount) {
        throw new NotFoundException(`Account with ID ${id} not found`);
      }

      // Update account status
      await queryRunner.manager.update(
        Account,
        { id: id },
        { isActive: activateDeactivateCompanyDto.isActive },
      );

      // Find all market centers for this company
      const marketCenters = await queryRunner.manager.find(MarketCenter, {
        where: { accountId: id, isDeleted: false },
      });

      for (const marketCenter of marketCenters) {
        // Update market center status
        await queryRunner.manager.update(MarketCenter, marketCenter.id, {
          isActive: activateDeactivateCompanyDto.isActive,
        });

        // Update all non-deleted market center users status
        await queryRunner.manager
          .createQueryBuilder()
          .update(MarketCenterUser)
          .set({ isActive: activateDeactivateCompanyDto.isActive })
          .where('market_center_id = :marketCenterId', {
            marketCenterId: marketCenter.id,
          })
          .andWhere('isDeleted = :isDeleted', { isDeleted: false })
          .execute();
      }

      // Update account users status
      await queryRunner.manager
        .createQueryBuilder()
        .update(AccountUser)
        .set({ isActive: activateDeactivateCompanyDto.isActive })
        .where('accountId = :accountId', {
          accountId: existingCompany['accountId'],
        })
        .andWhere('isDeleted = :isDeleted', { isDeleted: false })
        .execute();

      await queryRunner.commitTransaction();

      return await this.findCompanyById(id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async deleteCompanyById(id: string): Promise<boolean> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const account = await queryRunner.manager.findOne(Account, {
        where: { id, isDeleted: false },
      });

      if (!account) {
        throw new Error('Company not found');
      }

      if (account) {
        await queryRunner.manager.update(
          Account,
          { id: account.id },
          {
            isDeleted: true,
            isActive: false,
          },
        );
      }

      // Soft delete related market centers and their users
      const marketCenters = await queryRunner.manager.find(MarketCenter, {
        where: { accountId: id, isDeleted: false },
      });

      for (const marketCenter of marketCenters) {
        // Get all non-deleted market center users
        const marketCenterUsers = await queryRunner.manager.find(
          MarketCenterUser,
          {
            where: {
              marketCenter: { id: marketCenter.id },
              isDeleted: false,
            },
            relations: ['userRole', 'userRole.user'],
          },
        );

        // Process each market center user
        for (const mcu of marketCenterUsers) {
          // Soft delete the market center user
          await queryRunner.manager.update(MarketCenterUser, mcu.id, {
            isDeleted: true,
            isActive: false,
          });

          if (mcu.userRole) {
            // Check if user has other active market center user records
            const activeMarketCenterUserCount = await queryRunner.manager
              .createQueryBuilder(MarketCenterUser, 'mcu')
              .where('mcu.userRole.id = :userRoleId', {
                userRoleId: mcu.userRole.id,
              })
              .andWhere('mcu.isDeleted = false')
              .getCount();

            // If no other active market center user records exist
            if (activeMarketCenterUserCount === 0) {
              // Soft delete the user role
              await queryRunner.manager.update(UserRole, mcu.userRole.id, {
                isDeleted: true,
                isActive: false,
              });

              if (mcu.userRole.user) {
                // Check if user has any other active roles excluding market_center_manager
                const activeUserRoleCount = await queryRunner.manager
                  .createQueryBuilder(UserRole, 'ur')
                  .innerJoin('ur.role', 'r')
                  .where('ur.userId = :userId', {
                    userId: mcu.userRole.user.id,
                  })
                  .andWhere('ur.isDeleted = false')
                  .andWhere('r.name != :roleName', {
                    roleName: 'market_center_manager',
                  })
                  .getCount();

                // Only soft delete the user if they have no other roles except market_center_manager
                if (activeUserRoleCount === 0) {
                  await queryRunner.manager.update(User, mcu.userRole.user.id, {
                    isDeleted: true,
                    isActive: false,
                  });
                }
              }
            }
          }
        }

        // Soft delete the market center
        await queryRunner.manager.update(MarketCenter, marketCenter.id, {
          isDeleted: true,
          isActive: false,
        });
      }

      // Soft delete account users and handle company_admin roles
      const accountUsers = await queryRunner.manager.find(AccountUser, {
        where: { accountId: account.id },
        relations: ['userRole', 'userRole.user', 'userRole.role'],
      });

      for (const accountUser of accountUsers) {
        // Single update for account user
        await queryRunner.manager.update(AccountUser, accountUser.id, {
          isDeleted: true,
          isActive: false,
        });

        if (accountUser.userRole) {
          const isCompanyAdmin =
            accountUser.userRole.role?.name === 'company_admin';

          const activeRoleCount = await queryRunner.manager
            .createQueryBuilder(UserRole, 'ur')
            .where('ur.userId = :userId', {
              userId: accountUser.userRole.user.id,
            })
            .andWhere('ur.isDeleted = false')
            .getCount();

          if (activeRoleCount === 1 && isCompanyAdmin) {
            await queryRunner.manager.update(
              User,
              accountUser.userRole.user.id,
              {
                isDeleted: true,
                isActive: false,
              },
            );
            await queryRunner.manager.update(
              UserRole,
              accountUser.userRole.id,
              {
                isDeleted: true,
                isActive: false,
              },
            );
          } else if (activeRoleCount > 1 && isCompanyAdmin) {
            await queryRunner.manager.update(
              UserRole,
              accountUser.userRole.id,
              {
                isDeleted: true,
                isActive: false,
              },
            );
          }
        }
      }

      await queryRunner.commitTransaction();
      return true;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      Logger.log('Error soft deleting company:', error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async isCompanyExist(name: string, companyTypeId: string): Promise<boolean> {
    const company = await this.accountRepository
      .createQueryBuilder('account')
      .leftJoinAndSelect('account.accountDetail', 'accountDetail')
      .leftJoinAndSelect('accountDetail.companyType', 'companyType')
      .where('LOWER(account.name) = LOWER(:name)', { name })
      .andWhere('companyType.id = :companyTypeId', { companyTypeId })
      .andWhere('account.isDeleted = :isDeleted', { isDeleted: false })
      .getOne();

    return !!company;
  }

   parseDateRange(dateRange: string) {
    // Implement logic to parse dateRange string to { startDate, endDate }
    // e.g. "current_month", "last_month", "2024-06-01_to_2024-06-25"
    // Return { startDate: Date, endDate: Date }
  }

  // async getSummary(companyId: string, startDate: Date, endDate: Date) {
  //   const [marketCenters, agents, serviceProviders, properties, owners, homeDiaryActiveOwners] = await Promise.all([
  //     this.marketCenterRepo.count({ where: { companyId } }),
  //     this.agentRepo.count({ where: { companyId } }),
  //     this.spRepo.count({ where: { companyId } }),
  //     this.propertyRepo.count({ where: { companyId, status: 'APPROVED' } }),
  //     this.ownerRepo.count({ where: { companyId } }),
  //     this.ownerRepo.count({ where: { companyId, homeDiaryUpdated: true } }),
  //   ]);
  //   return {
  //     marketCenters,
  //     agents,
  //     serviceProviders,
  //     properties,
  //     owners,
  //     homeDiaryActiveOwners,
  //   };
  // }

  // async getJobs(companyId: string, startDate: Date, endDate: Date) {
  //   const [ongoing, pending, completed] = await Promise.all([
  //     this.jobRepo.count({ where: { companyId, status: 'ONGOING', createdAt: Between(startDate, endDate) } }),
  //     this.jobRepo.count({ where: { companyId, status: 'PENDING', createdAt: Between(startDate, endDate) } }),
  //     this.jobRepo.count({ where: { companyId, status: 'COMPLETED', createdAt: Between(startDate, endDate) } }),
  //   ]);
  //   return {
  //     total: ongoing + pending + completed,
  //     ongoing,
  //     pending,
  //     completed,
  //   };
  // }

  async getRevenueByCategories(companyId: string, startDate: Date, endDate: Date) {
    // Example: group by category and sum revenue
    // return this.revenueRepo
    //   .createQueryBuilder('revenue')
    //   .select('revenue.category', 'name')
    //   .addSelect('SUM(revenue.amount)', 'value')
    //   .where('revenue.companyId = :companyId', { companyId })
    //   .andWhere('revenue.createdAt BETWEEN :start AND :end', { start: startDate, end: endDate })
    //   .groupBy('revenue.category')
    //   .getRawMany();
  }

  async getTotalCommission(companyId: string, startDate: Date, endDate: Date) {
    // Example: sum commission by type
    // const commissions = await this.commissionRepo
    //   .createQueryBuilder('commission')
    //   .select('commission.type', 'type')
    //   .addSelect('SUM(commission.amount)', 'amount')
    //   .where('commission.companyId = :companyId', { companyId })
    //   .andWhere('commission.createdAt BETWEEN :start AND :end', { start: startDate, end: endDate })
    //   .groupBy('commission.type')
    //   .getRawMany();

    // let total = 0, marketCenter = 0, agent = 0, company = 0;
    // commissions.forEach(c => {
    //   total += Number(c.amount);
    //   if (c.type === 'MARKET_CENTER') marketCenter = Number(c.amount);
    //   if (c.type === 'AGENT') agent = Number(c.amount);
    //   if (c.type === 'COMPANY') company = Number(c.amount);
    // });
    // return { total, marketCenter, agent, company };
  }

  // async getTopMarketCenters(companyId: string, startDate: Date, endDate: Date) {
  //   // Example: top 5 by revenue
  //   return this.marketCenterRepo
  //     .createQueryBuilder('mc')
  //     .leftJoinAndSelect('mc.jobs', 'job')
  //     .leftJoinAndSelect('mc.revenues', 'revenue')
  //     .where('mc.companyId = :companyId', { companyId })
  //     .andWhere('job.createdAt BETWEEN :start AND :end', { start: startDate, end: endDate })
  //     .select([
  //       'mc.name AS name',
  //       'COUNT(job.id) AS jobs',
  //       'SUM(revenue.amount) AS revenue',
  //       'SUM(revenue.commission) AS commission'
  //     ])
  //     .groupBy('mc.id')
  //     .orderBy('revenue', 'DESC')
  //     .limit(5)
  //     .getRawMany();
  // }

  // async getTopAgents(companyId: string, startDate: Date, endDate: Date) {
  //   // Example: top 5 by revenue
  //   return this.agentRepo
  //     .createQueryBuilder('agent')
  //     .leftJoinAndSelect('agent.revenues', 'revenue')
  //     .where('agent.companyId = :companyId', { companyId })
  //     .andWhere('revenue.createdAt BETWEEN :start AND :end', { start: startDate, end: endDate })
  //     .select([
  //       'agent.name AS name',
  //       'SUM(revenue.amount) AS revenue',
  //       'SUM(revenue.commission) AS commission'
  //     ])
  //     .groupBy('agent.id')
  //     .orderBy('revenue', 'DESC')
  //     .limit(5)
  //     .getRawMany();
  // }
}
