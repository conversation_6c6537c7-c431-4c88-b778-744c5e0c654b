import { In, Repository, Not } from 'typeorm';
import { MarketCenter } from '../entities/market-center.entity';
import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateMarketCenterDto } from '../dto/create-market-center.dto';
import { Address } from '../../users/entities/address.entity';
import { User } from '../../users/entities/user.entity';
import * as bcrypt from 'bcrypt';
import { MarketCenterUser } from '../entities/market-center-user.entity';
import { UserRole } from '../../role/entities/user-role.entity';
import { RoleRepository } from '../../role/repositories/role.repository';
import { UpdateMarketCenterDto } from '../dto/update-market-center.dto';
import { ZipCode } from '../../zip-codes/entities/zip-code.entity';
import { CommonService } from '../../common/utils/common.service';
import { PageOptionsDto } from '../../common/pagination/pageOptions.dto';
import { SortSearchDataDto } from '../../common/pagination/sort-search-data.dto';
import { MarketCenterFilterDto } from '../dto/filter-market-center.dto';
import { Identifier } from '@shared-types';
import { Account } from '../../accounts/entities/account.entity';

@Injectable()
export class MarketCentersRepository extends Repository<MarketCenter> {
  constructor(
    @InjectRepository(MarketCenter)
    private marketCenterRepository: Repository<MarketCenter>,
    // This repository is required for TypeORM to properly set up the entity manager
    // Even though we don't directly use it, it's needed for the In(serviceAreaIds) query
    @InjectRepository(ZipCode)
    private readonly zipCityStateRepository: Repository<ZipCode>,
    private roleRepository: RoleRepository,
    private readonly commonService: CommonService,
  ) {
    super(
      marketCenterRepository.target,
      marketCenterRepository.manager,
      marketCenterRepository.queryRunner,
    );
  }

  // Helper function to check if user already has market_center_manager role for specific market center
  async checkExistingMarketCenterRole(
    transactionalEntityManager: any,
    userId: string,
    marketCenterId: string,
  ) {
    return await transactionalEntityManager
      .createQueryBuilder(MarketCenterUser, 'mcu')
      .innerJoinAndSelect('mcu.userRole', 'ur')
      .innerJoinAndSelect('ur.role', 'r')
      .where('ur.userId = :userId', { userId })
      .andWhere('mcu.market_center_id = :marketCenterId', { marketCenterId }) // Fixed column name
      .andWhere('r.name = :roleName', { roleName: 'market_center_manager' })
      .andWhere('mcu.isDeleted = :isDeleted', { isDeleted: false })
      .getOne();
  }

  async createMarketCenterWithUserAddressAndServiceAbleAreas(
    createMarketCenterDto: CreateMarketCenterDto,
  ) {
    const { name, phoneNumber, serviceAreaIds } = createMarketCenterDto;
    const marketCenterManagerRole =
      await this.roleRepository.findRoleByRoleName('market_center_manager');

    return this.manager.transaction(async (transactionalEntityManager) => {
      // Find all service areas
      const serviceAreas = await transactionalEntityManager.findBy(ZipCode, {
        id: In(serviceAreaIds),
      });

      if (serviceAreas.length !== serviceAreaIds.length) {
        throw new NotFoundException('Some service areas were not found');
      }

      let address = new Address();
      address = { ...address, ...createMarketCenterDto.address } as Address;
      const savedAddress = await transactionalEntityManager.save(
        Address,
        address,
      );
      const account = await transactionalEntityManager.findOneBy(Account, {
        id: createMarketCenterDto.accountId,
      });
      if (!account) {
        throw new NotFoundException('Account not found');
      }

      let marketCenter = new MarketCenter();
      marketCenter = {
        ...marketCenter,
        name,
        phoneNumber,
        address: savedAddress,
        code: createMarketCenterDto.code,
        serviceAreas,
        accountId: createMarketCenterDto.accountId,
        account,
      };
      const savedMarketCenter = await transactionalEntityManager.save(
        MarketCenter,
        marketCenter,
      );

      // Create array to store user data with plain passwords
      const usersWithPlainPasswords = [];

      for (const user of createMarketCenterDto.users) {
        let savedUser: User;
        let plainPassword: string | null = null;

        // Check if user exists
        const existingUser = await transactionalEntityManager.findOne(User, {
          where: [
            { username: user.username.toLowerCase(), isDeleted: false },
            { email: user.username.toLowerCase(), isDeleted: false },
          ],
        });

        if (existingUser) {
          // Check if user already has market_center_manager role for this specific market center
          const existingMarketCenterUser =
            await this.checkExistingMarketCenterRole(
              transactionalEntityManager,
              existingUser.id,
              savedMarketCenter.id,
            );

          if (existingMarketCenterUser) {
            throw new ConflictException(
              `User is already a manager for this market center`,
            );
          }
          savedUser = existingUser;
        } else {
          // Create new user
          const { hashedPassword, password: generatedPassword } =
            await this.commonService.generateSecurePassword();

          const newUser = {
            ...{
              ...user,
              username: user.username.toLowerCase(),
              email: user.username.toLowerCase(),
            },
            password: hashedPassword,
          };
          savedUser = await transactionalEntityManager.save(User, newUser);
          plainPassword = generatedPassword;
        }

        // Create or get market_center_manager role for the user
        let userRole = await transactionalEntityManager.findOne(UserRole, {
          where: {
            userId: savedUser.id,
            roleId: marketCenterManagerRole.id,
            isActive: true,
          },
        });

        if (!userRole) {
          // Create new UserRole if it doesn't exist
          userRole = await transactionalEntityManager.save(UserRole, {
            user: savedUser,
            role: marketCenterManagerRole,
            userId: savedUser.id,
            roleId: marketCenterManagerRole.id,
          });
        }

        // Create market center user association
        let marketCenterUserRole = new MarketCenterUser();
        marketCenterUserRole = {
          ...marketCenterUserRole,
          marketCenter: savedMarketCenter,
          userRole: userRole,
        };
        await transactionalEntityManager.save(
          MarketCenterUser,
          marketCenterUserRole,
        );

        // Add user to usersWithPlainPasswords array
        usersWithPlainPasswords.push({
          ...savedUser,
          plainPassword, // Will be null for existing users
        });
      }
      const updatedMarketCenter = await transactionalEntityManager
        .createQueryBuilder(MarketCenter, 'marketCenter')
        .leftJoinAndSelect('marketCenter.address', 'address')
        .leftJoinAndSelect(
          'marketCenter.marketCenterUsers',
          'marketCenterUsers',
        )
        .leftJoinAndSelect('marketCenterUsers.userRole', 'userRole')
        .leftJoinAndSelect('userRole.role', 'role')
        .leftJoinAndSelect('userRole.user', 'user')
        .leftJoinAndSelect('marketCenter.serviceAreas', 'serviceAreas')
        .leftJoinAndSelect('serviceAreas.city', 'city')
        .leftJoinAndSelect('serviceAreas.state', 'state')
        .leftJoinAndSelect('marketCenter.account', 'account')

        .where('marketCenter.id = :id', { id: savedMarketCenter.id })
        .select([
          'marketCenter',
          'address',
          'marketCenterUsers',
          'userRole.id',
          'role.id',
          'role.name',
          'user.email',
          'user.firstName',
          'user.id',
          'user.lastName',
          'serviceAreas.zipCode',
          'serviceAreas.id',
          'city.id',
          'city.cityName',
          'state.id',
          'state.stateName',
          'account.id',
          'account.name',
        ])
        .getOne();

      // Debug log to help diagnose issues

      // Attach the plain passwords to the response
      if (updatedMarketCenter && updatedMarketCenter.marketCenterUsers) {
        updatedMarketCenter.marketCenterUsers =
          updatedMarketCenter.marketCenterUsers.map((mcu) => {
            // Find the corresponding user with plain password
            const userWithPlainPassword = usersWithPlainPasswords.find(
              (u) => u.id === mcu.userRole.user.id,
            );

            return {
              ...mcu,
              userRole: {
                ...mcu.userRole,
                user: {
                  ...mcu.userRole.user,
                  // Only add plainPassword if it exists
                  ...(userWithPlainPassword
                    ? { plainPassword: userWithPlainPassword.plainPassword }
                    : {}),
                },
              },
            };
          });
      }

      return updatedMarketCenter;
    });
  }

  async updateMarketCenterWithDependencies(
    id: string,
    updateMarketCenterDto: UpdateMarketCenterDto,
  ) {
    return this.manager.transaction(async (transactionalEntityManager) => {
      // Find existing market center
      const existingMarketCenter = await transactionalEntityManager.findOne(
        MarketCenter,
        {
          where: { id },
          relations: [
            'address',
            'marketCenterUsers',
            'marketCenterUsers.userRole',
            'marketCenterUsers.userRole.user',
            'marketCenterUsers.userRole.role',
            'serviceAreas',
            'serviceAreas.city',
            'serviceAreas.state',
          ],
        },
      );

      if (!existingMarketCenter) {
        throw new NotFoundException('Market Center not found');
      }

      // Update service areas if provided
      if (updateMarketCenterDto.serviceAreaIds) {
        const serviceAreas = await transactionalEntityManager.findBy(ZipCode, {
          id: In(updateMarketCenterDto.serviceAreaIds),
        });

        if (
          serviceAreas.length !== updateMarketCenterDto.serviceAreaIds.length
        ) {
          throw new NotFoundException('Some service areas were not found');
        }

        existingMarketCenter.serviceAreas = serviceAreas;
      }

      // Update address if provided
      if (updateMarketCenterDto.address) {
        // First find the existing address to ensure it exists
        const existingAddress = await transactionalEntityManager.findOne(
          Address,
          {
            where: { id: existingMarketCenter.address.id },
          },
        );

        if (!existingAddress) {
          throw new NotFoundException('Address not found');
        }

        // Merge the updates with existing address
        const updatedAddress = {
          ...existingAddress,
          ...updateMarketCenterDto.address,
        };

        // Save the updated address
        await transactionalEntityManager.save(Address, updatedAddress);

        // Update the address in memory as well
        existingMarketCenter.address = updatedAddress;
      }

      // Update market center basic info
      const marketCenterUpdate = {
        name: updateMarketCenterDto.name,
        phoneNumber: updateMarketCenterDto.phoneNumber,
      };

      // Save the market center with updated service areas
      await transactionalEntityManager.save(MarketCenter, {
        ...existingMarketCenter,
        ...marketCenterUpdate,
      });

      // Create array to store user data with plain passwords
      const usersWithPlainPasswords = [];

      // Update or create users if provided
      if (updateMarketCenterDto.users?.length) {
        const marketCenterManagerRole =
          await this.roleRepository.findRoleByRoleName('market_center_manager');

        for (let userUpdate of updateMarketCenterDto.users) {
          let savedUser: User;
          let plainPassword: string | null = null;

          if (userUpdate.marketCenterUserId) {
            // Update existing market center user
            let marketCenterUser =
              existingMarketCenter.marketCenterUsers.find(
                (mcu) => mcu.id === userUpdate.marketCenterUserId,
              );

            if (marketCenterUser) {
              let userUpdateData = { ...userUpdate };
              delete userUpdateData.marketCenterUserId;

              // Hash password if it's being updated
              if (userUpdateData.password) {
                let salt = await bcrypt.genSalt();
                userUpdateData.password = await bcrypt.hash(
                  userUpdateData.password,
                  salt,
                );
              }

              // If email is being updated, check if it exists for any other user
              if (
                userUpdateData.email &&
                userUpdateData.email !== marketCenterUser.userRole.user.email
              ) {
                let existingUserWithEmail =
                  await transactionalEntityManager.findOne(User, {
                    where: [
                      {
                        email: userUpdateData.email.toLowerCase(),
                        isDeleted: false,
                        id: Not(marketCenterUser.userRole.user.id),
                      },
                    ],
                  });

                if (existingUserWithEmail) {
                  throw new ConflictException(
                    `Email ${userUpdateData.email} already exists`,
                  );
                }
              }

              // Update user
              await transactionalEntityManager.update(
                User,
                marketCenterUser.userRole.user.id,
                {
                  ...userUpdateData,
                  email: userUpdateData.email?.toLowerCase(),
                },
              );
            } else {
              throw new NotFoundException(`Market center user not found`);
            }
          } else {
            // Handle new user addition
            // Check if user already exists
            let existingUser = await transactionalEntityManager.findOne(
              User,
              {
                where: [
                  {
                    username: userUpdate.username.toLowerCase(),
                    isDeleted: false,
                  },
                  {
                    email: userUpdate.username.toLowerCase(),
                    isDeleted: false,
                  },
                ],
              },
            );

            if (existingUser) {
              // Check if user already has market_center_manager role for this specific market center
              let existingMarketCenterUser =
                await this.checkExistingMarketCenterRole(
                  transactionalEntityManager,
                  existingUser.id,
                  existingMarketCenter.id,
                );

              if (existingMarketCenterUser) {
                throw new ConflictException(
                  `User is already a manager for this market center`,
                );
              }
              savedUser = existingUser;
              usersWithPlainPasswords.push({
                ...savedUser,
                plainPassword: null,
                marketCenterUserId: null,
              });
            } else {
              // Create new user
              let { hashedPassword, password: generatedPassword } =
                await this.commonService.generateSecurePassword();
              let newUserData = {
                ...userUpdate,
                password: hashedPassword,
                email: userUpdate.username.toLowerCase(),
              };

              savedUser = await transactionalEntityManager.save(
                User,
                newUserData,
              );
              plainPassword = generatedPassword;

              usersWithPlainPasswords.push({
                ...savedUser,
                plainPassword,
                marketCenterUserId: null,
              });
            }

            // Create or get market_center_manager role
            let userRole = await transactionalEntityManager.findOne(UserRole, {
              where: {
                userId: savedUser.id,
                roleId: marketCenterManagerRole.id,
                isActive: true,
              },
              relations: ['user', 'role'],
            });

            if (!userRole) {
              // Create new UserRole
              userRole = await transactionalEntityManager.save(UserRole, {
                role: marketCenterManagerRole,
                user: savedUser,
                roleId: marketCenterManagerRole.id,
                userId: savedUser.id,
              });
            }

            // Create market center user association
            let marketCenterUser = new MarketCenterUser();
            marketCenterUser = {
              ...marketCenterUser,
              marketCenter: {id: existingMarketCenter.id} as any,
             
              userRole: userRole,
            };
            await transactionalEntityManager.save(
              MarketCenterUser,
              marketCenterUser,
            );
          }
        }
      }

      // Get the marketCenterUserIds that were provided in the update
      const providedMarketCenterUserIds = updateMarketCenterDto.users
        .filter((user) => user.marketCenterUserId)
        .map((user) => user.marketCenterUserId);
      if (providedMarketCenterUserIds?.length > 0) {
        const marketCenterUserIdsToDelete =
          existingMarketCenter.marketCenterUsers
            .filter((mcu) => !providedMarketCenterUserIds.includes(mcu.id))
            .map((mcu) => mcu.id);
        if (marketCenterUserIdsToDelete.length) {
          // First soft delete the market center users
          await transactionalEntityManager
            .createQueryBuilder()
            .update(MarketCenterUser)
            .set({
              isDeleted: true,
              isActive: false,
            })
            .where('id IN (:...marketCenterUserIds)', {
              marketCenterUserIds: marketCenterUserIdsToDelete,
            })
            .andWhere('isDeleted = :isDeleted', { isDeleted: false })
            .execute();

          // Get all userRoleIds that were associated with these market center users
          const affectedUserRoles = await transactionalEntityManager
            .createQueryBuilder(MarketCenterUser, 'mcu')
            .select('mcu.userRole.id', 'userRoleId')
            .where('mcu.id IN (:...marketCenterUserIds)', {
              marketCenterUserIds: marketCenterUserIdsToDelete,
            })
            .getRawMany();

          const userRoleIds = affectedUserRoles.map((ur) => ur.userRoleId);

          // For each userRole, check if it's used in any other active market center
          for (const userRoleId of userRoleIds) {
            const activeMarketCenterUserCount = await transactionalEntityManager
              .createQueryBuilder(MarketCenterUser, 'mcu')
              .where('mcu.userRole.id = :userRoleId', { userRoleId })
              .andWhere('mcu.isDeleted = false')
              .getCount();

            // If no active marketCenterUser exists, soft delete the userRole
            if (activeMarketCenterUserCount === 0) {
              await transactionalEntityManager
                .createQueryBuilder()
                .update(UserRole)
                .set({
                  isDeleted: true,
                  isActive: false,
                })
                .where('id = :userRoleId', { userRoleId })
                .execute();

              // Check if the user associated with this userRole has any other active userRoles
              const userId = (
                await transactionalEntityManager
                  .createQueryBuilder(UserRole, 'ur')
                  .select('ur.userId', 'userId')
                  .where('ur.id = :userRoleId', { userRoleId })
                  .getRawOne()
              )?.userId;

              if (userId) {
                const activeUserRoleCount = await transactionalEntityManager
                  .createQueryBuilder(UserRole, 'ur')
                  .where('ur.userId = :userId', { userId })
                  .andWhere('ur.isDeleted = false')
                  .getCount();

                // If no active userRoles exist, soft delete the user
                if (activeUserRoleCount === 0) {
                  await transactionalEntityManager
                    .createQueryBuilder()
                    .update(User)
                    .set({
                      isDeleted: true,
                      isActive: false,
                    })
                    .where('id = :userId', { userId })
                    .execute();
                }
              }
            }
          }
        }
      } else {
        // If no users provided in update, soft delete all existing market center users
        const marketCenterUserIds = existingMarketCenter.marketCenterUsers.map(
          (mcu) => mcu.id,
        );

        if (marketCenterUserIds.length) {
          // First soft delete the market center users
          await transactionalEntityManager
            .createQueryBuilder()
            .update(MarketCenterUser)
            .set({
              isDeleted: true,
              isActive: false,
            })
            .where('id IN (:...marketCenterUserIds)', {
              marketCenterUserIds,
            })
            .andWhere('isDeleted = :isDeleted', { isDeleted: false })
            .execute();

          // Get all userRoleIds that were associated with these market center users
          const affectedUserRoles = await transactionalEntityManager
            .createQueryBuilder(MarketCenterUser, 'mcu')
            .select('mcu.userRole.id', 'userRoleId')
            .where('mcu.id IN (:...marketCenterUserIds)', {
              marketCenterUserIds,
            })
            .getRawMany();

          const userRoleIds = affectedUserRoles.map((ur) => ur.userRoleId);

          // For each userRole, check if it's used in any other active market center
          for (const userRoleId of userRoleIds) {
            const activeMarketCenterUserCount = await transactionalEntityManager
              .createQueryBuilder(MarketCenterUser, 'mcu')
              .where('mcu.userRole.id = :userRoleId', { userRoleId })
              .andWhere('mcu.isDeleted = false')
              .getCount();

            // If no active marketCenterUser exists, soft delete the userRole
            if (activeMarketCenterUserCount === 0) {
              await transactionalEntityManager
                .createQueryBuilder()
                .update(UserRole)
                .set({
                  isDeleted: true,
                  isActive: false,
                })
                .where('id = :userRoleId', { userRoleId })
                .execute();

              // Check if the user associated with this userRole has any other active userRoles
              const userId = (
                await transactionalEntityManager
                  .createQueryBuilder(UserRole, 'ur')
                  .select('ur.userId', 'userId')
                  .where('ur.id = :userRoleId', { userRoleId })
                  .getRawOne()
              )?.userId;

              if (userId) {
                const activeUserRoleCount = await transactionalEntityManager
                  .createQueryBuilder(UserRole, 'ur')
                  .where('ur.userId = :userId', { userId })
                  .andWhere('ur.isDeleted = false')
                  .getCount();

                // If no active userRoles exist, soft delete the user
                if (activeUserRoleCount === 0) {
                  await transactionalEntityManager
                    .createQueryBuilder()
                    .update(User)
                    .set({
                      isDeleted: true,
                      isActive: false,
                    })
                    .where('id = :userId', { userId })
                    .execute();
                }
              }
            }
          }
        }
      }

      const updatedMarketCenter = await transactionalEntityManager
        .createQueryBuilder(MarketCenter, 'marketCenter')
        .leftJoinAndSelect('marketCenter.address', 'address')
        .leftJoinAndSelect(
          'marketCenter.marketCenterUsers',
          'marketCenterUsers',
        )
        .leftJoinAndSelect('marketCenterUsers.userRole', 'userRole')
        .leftJoinAndSelect('userRole.role', 'role')
        .leftJoinAndSelect('userRole.user', 'user')
        .where('marketCenter.id = :id', { id })
        .select([
          'marketCenter.id',
          'marketCenter.name',
          'marketCenter.isActive',
          'address.id',
          'address.zipCode',
          'address.street',
          'address.city',
          'address.state',
          'marketCenterUsers',
          'userRole',
          'role.id',
          'role.name',
          'user.id',
          'user.firstName',
          'user.lastName',
          'user.username',
          'user.isActive',
          'user.email',
        ])
        .getOne();

      // Debug log to help diagnose issues

      // Attach the plain passwords to the response for new users
      if (
        updatedMarketCenter &&
        updatedMarketCenter.marketCenterUsers &&
        usersWithPlainPasswords.length > 0
      ) {
        updatedMarketCenter.marketCenterUsers =
          updatedMarketCenter.marketCenterUsers.map((mcu) => {
            const userWithPassword = usersWithPlainPasswords.find(
              (u) => u.id === mcu.userRole.user.id,
            );
            if (userWithPassword) {
              return {
                ...mcu,
                userRole: {
                  ...mcu.userRole,
                  user: {
                    ...mcu.userRole.user,
                    plainPassword: userWithPassword.plainPassword,
                  },
                },
              };
            }
            return mcu;
          });
      }

      return updatedMarketCenter;
    });
  }

  async findMarketCentersWithFilters(
    pageOptionsDto: PageOptionsDto | null,
    sortSearchDataDto: SortSearchDataDto,
    filterMarketCenterDto: MarketCenterFilterDto,
  ) {
    // Create query builder
    const queryBuilder = this.createQueryBuilder('marketCenter')
      .select('DISTINCT marketCenter.id')
      .leftJoinAndSelect('marketCenter.address', 'address')
      .leftJoinAndSelect(
        'marketCenter.marketCenterUsers',
        'marketCenterUsers',
        'marketCenterUsers.isDeleted = false',
      )
      .leftJoinAndSelect(
        'marketCenterUsers.userRole',
        'userRole',
        'userRole.isActive = true',
      )
      .leftJoinAndSelect('userRole.role', 'role')
      .leftJoinAndSelect('userRole.user', 'user')
      .leftJoinAndSelect('marketCenter.serviceAreas', 'serviceAreas')
      .leftJoinAndSelect('serviceAreas.city', 'city')
      .leftJoinAndSelect('serviceAreas.state', 'state')
      .leftJoinAndSelect('marketCenter.account', 'account')
      .where('marketCenter.isDeleted = :isDeleted', { isDeleted: false });

    // Get total count without filters for comparison
    const totalRecordsWithoutFilters = await this.createQueryBuilder('marketCenter')
      .where('marketCenter.isDeleted = :isDeleted', { isDeleted: false })
      .getCount();
  

         if (filterMarketCenterDto.ids?.length) {
      queryBuilder.andWhere('marketCenter.id IN (:...selectedIds)', {
        selectedIds: filterMarketCenterDto.ids,
      });
    }


    // Apply account filter if accountId is provided and not '0'
    if (
      filterMarketCenterDto.accountId &&
      filterMarketCenterDto.accountId !== '0'
    ) {
      queryBuilder.andWhere('account.id = :accountId', {
        accountId: filterMarketCenterDto.accountId,
      });
    }

    if (filterMarketCenterDto.marketCenterIds?.length) {
      queryBuilder.andWhere('marketCenter.id IN (:...marketCenterIds)', {
        marketCenterIds: filterMarketCenterDto.marketCenterIds,
      });
    }

    if (filterMarketCenterDto.createdDateRange?.length === 2) {
      queryBuilder.andWhere(
        'marketCenter.createdAt BETWEEN :startDate AND :endDate',
        {
          startDate: filterMarketCenterDto.createdDateRange[0],
          endDate: filterMarketCenterDto.createdDateRange[1],
        },
      );
    }

    if (filterMarketCenterDto.isActive !== undefined) {
      queryBuilder.andWhere('marketCenter.isActive = :isActive', {
        isActive: filterMarketCenterDto.isActive,
      });
    }

    if (filterMarketCenterDto.marketCenterUserFirstName) {
      queryBuilder.andWhere(
        'LOWER(user.firstName) ILIKE LOWER(:firstName)',
        {
          firstName: `%${filterMarketCenterDto.marketCenterUserFirstName}%`,
        },
      );
    }

    if (filterMarketCenterDto.marketCenterUserLastName) {
      queryBuilder.andWhere(
        'LOWER(user.lastName) ILIKE LOWER(:lastName)',
        {
          lastName: `%${filterMarketCenterDto.marketCenterUserLastName}%`,
        },
      );
    }

    if (filterMarketCenterDto.cities?.length) {
      queryBuilder.andWhere('city.id IN (:...cityIds)', {
        cityIds: filterMarketCenterDto.cities,
      });
    }

    if (filterMarketCenterDto.states?.length) {
      queryBuilder.andWhere('address.state IN (:...stateIds)', {
        stateIds: filterMarketCenterDto.states,
      });
    }

    if (sortSearchDataDto.searchText) {
      queryBuilder.andWhere('LOWER(marketCenter.name) ILIKE LOWER(:searchText)', {
        searchText: `%${sortSearchDataDto.searchText}%`,
      });
    }

    // Add address cities filter
    if (filterMarketCenterDto.addressCities?.length) {
      const cityConditions = filterMarketCenterDto.addressCities.map(
        (city, index) => {
          const paramName = `addressCity${index}`;
          queryBuilder.setParameter(paramName, `%${city}%`);
          return `LOWER(address.city) ILIKE :${paramName}`;
        },
      );

      queryBuilder.andWhere(`(${cityConditions.join(' OR ')})`);
    }

    // Add address states filter
    if (filterMarketCenterDto.addressStates?.length) {
      const stateConditions = filterMarketCenterDto.addressStates.map(
        (state, index) => {
          const paramName = `addressState${index}`;
          queryBuilder.setParameter(paramName, `%${state}%`);
          return `LOWER(address.state) ILIKE :${paramName}`;
        },
      );

      queryBuilder.andWhere(`(${stateConditions.join(' OR ')})`);
    }

    // Apply sorting
    if (sortSearchDataDto.sortColumn) {
      switch (sortSearchDataDto.sortColumn) {
        case 'addressState':
          queryBuilder.orderBy(
            'address.state',
            sortSearchDataDto.sortOrder || 'ASC',
          );
          break;
        case 'addressCity':
          queryBuilder.orderBy(
            'address.city',
            sortSearchDataDto.sortOrder || 'ASC',
          );
          break;
        case 'company':
          queryBuilder.orderBy(
            'account.name',
            sortSearchDataDto.sortOrder || 'ASC',
          );
          break;
        default:
          queryBuilder.orderBy(
            `marketCenter.${sortSearchDataDto.sortColumn}`,
            sortSearchDataDto.sortOrder || 'ASC',
          );
      }
    } else {
      queryBuilder.orderBy('marketCenter.createdAt', 'DESC');
    }

    // Apply pagination if pageOptionsDto is provided
    if (pageOptionsDto !== null && pageOptionsDto.paginate !== false) {
      queryBuilder.skip((pageOptionsDto.page - 1) * pageOptionsDto.limit);
      queryBuilder.take(pageOptionsDto.limit);
    }

    const [marketCenters, totalRecords] = await queryBuilder
      .select([
        'marketCenter.id',
        'marketCenter.name',
        'marketCenter.isActive',
        'marketCenter.createdAt',
        'marketCenter.code',
        'marketCenter.phoneNumber',
        'address.street',
        'address.city',
        'address.state',
        'address.zipcode',
        'marketCenterUsers.id',
        'marketCenterUsers.isDeleted',
        'marketCenterUsers.isActive',
        'marketCenterUsers.createdAt',
        'marketCenterUsers.updatedAt',
        'userRole.id',
        'userRole.isActive',
        'user.firstName',
        'user.lastName',
        'user.contactNumber',
        'user.username',
        'user.email',
        'user.isActive',
        'user.profileImageUrl',
        'user.id',
        'role.name',
        'role.id',
        'serviceAreas.id',
        'serviceAreas.zipCode',
        'city.cityName',
        'city.id',
        'state.id',
        'state.stateName',
        'account.id',
        'account.name',
      ])
      .getManyAndCount();

    // Transform the marketCenters
    const transformedMarketCenters = marketCenters.map((marketCenter) => {
      const { account, ...restMarketCenter } = marketCenter;
      return {
        ...restMarketCenter,
        company: {
          id: account.id,
          name: account.name,
        },
        marketCenterUsers: marketCenter.marketCenterUsers.map((mcu) => ({
          id: mcu.id,
          isActive: mcu.isActive,
          isDeleted: mcu.isDeleted,
          createdAt: mcu.createdAt,
          updatedAt: mcu.updatedAt,
          user: mcu.userRole.user,
          role: mcu.userRole.role,
          marketCenterUserStatus:
            mcu.isActive && mcu.userRole.isActive && mcu.userRole.user.isActive,
        })),
      };
    });

    return {
      marketCenters: transformedMarketCenters,
      totalRecords,
      totalRecordsWithoutFilters,
    };
  }

  async changeMarketCenterStatus(
    id: string,
    isActive: boolean,
  ): Promise<MarketCenter> {
    return this.manager.transaction(async (transactionalEntityManager) => {
      const marketCenter = await transactionalEntityManager.findOne(
        MarketCenter,
        {
          where: { id, isDeleted: false },
          relations: ['marketCenterUsers'],
        },
      );

      if (!marketCenter) {
        throw new NotFoundException('Market center not found');
      }

      // Update market center
      const updatedMarketCenter = await transactionalEntityManager.save(
        MarketCenter,
        {
          ...marketCenter,
          isActive,
        },
      );

      return updatedMarketCenter;
    });
  }

  async softDeleteMarketCenter(id: Identifier): Promise<MarketCenter> {
    return this.manager.transaction(async (transactionalEntityManager) => {
      const marketCenter = await transactionalEntityManager.findOne(
        MarketCenter,
        {
          where: { id, isDeleted: false },
          relations: ['marketCenterUsers'],
        },
      );

      if (!marketCenter) {
        throw new NotFoundException('Market center not found');
      }

      // Update market center
      const updatedMarketCenter = await transactionalEntityManager.save(
        MarketCenter,
        {
          ...marketCenter,
          isDeleted: true,
          isActive: false,
        },
      );

      // Update all non-deleted market center users
      if (marketCenter.marketCenterUsers?.length) {
        const marketCenterUserIds = marketCenter.marketCenterUsers
          .filter((mcu) => !mcu.isDeleted)
          .map((mcu) => mcu.id);

        if (marketCenterUserIds.length) {
          // First soft delete the market center users
          await transactionalEntityManager
            .createQueryBuilder()
            .update(MarketCenterUser)
            .set({
              isDeleted: true,
              isActive: false,
            })
            .where('id IN (:...marketCenterUserIds)', { marketCenterUserIds })
            .andWhere('isDeleted = :isDeleted', { isDeleted: false })
            .execute();

          // Get all userRoleIds that were associated with these market center users
          const affectedUserRoles = await transactionalEntityManager
            .createQueryBuilder(MarketCenterUser, 'mcu')
            .select('mcu.userRole.id', 'userRoleId')
            .where('mcu.id IN (:...marketCenterUserIds)', {
              marketCenterUserIds,
            })
            .getRawMany();

          const userRoleIds = affectedUserRoles.map((ur) => ur.userRoleId);

          // For each affected userRole, check if it has any other active marketCenterUser
          for (const userRoleId of userRoleIds) {
            const activeMarketCenterUserCount = await transactionalEntityManager
              .createQueryBuilder(MarketCenterUser, 'mcu')
              .where('mcu.userRole.id = :userRoleId', { userRoleId })
              .andWhere('mcu.isDeleted = false')
              .getCount();

            // If no active marketCenterUser exists, soft delete the userRole
            if (activeMarketCenterUserCount === 0) {
              await transactionalEntityManager
                .createQueryBuilder()
                .update(UserRole)
                .set({
                  isDeleted: true,
                  isActive: false,
                })
                .where('id = :userRoleId', { userRoleId })
                .execute();

              // Check if the user associated with this userRole has any other active userRoles
              const userId = (
                await transactionalEntityManager
                  .createQueryBuilder(UserRole, 'ur')
                  .select('ur.userId', 'userId')
                  .where('ur.id = :userRoleId', { userRoleId })
                  .getRawOne()
              )?.userId;

              if (userId) {
                const activeUserRoleCount = await transactionalEntityManager
                  .createQueryBuilder(UserRole, 'ur')
                  .where('ur.userId = :userId', { userId })
                  .andWhere('ur.isDeleted = false')
                  .getCount();

                // If no active userRoles exist, soft delete the user
                if (activeUserRoleCount === 0) {
                  await transactionalEntityManager
                    .createQueryBuilder()
                    .update(User)
                    .set({
                      isDeleted: true,
                      isActive: false,
                    })
                    .where('id = :userId', { userId })
                    .execute();
                }
              }
            }
          }
        }
      }

      return updatedMarketCenter;
    });
  }

  async findMarketCenterById(id: string): Promise<object> {
    const marketCenter = await this.marketCenterRepository
      .createQueryBuilder('marketCenter')
      .leftJoinAndSelect('marketCenter.address', 'address')
      .leftJoinAndSelect('marketCenter.marketCenterUsers', 'marketCenterUsers')
      .leftJoinAndSelect('marketCenterUsers.userRole', 'userRole')
      .leftJoinAndSelect('userRole.user', 'user')
      .leftJoinAndSelect('userRole.role', 'role')
      .leftJoinAndSelect('user.userRoles', 'allUserRoles')
      .leftJoinAndSelect('allUserRoles.role', 'allRoles')
      .leftJoinAndSelect('marketCenter.serviceAreas', 'serviceAreas')
      .leftJoinAndSelect('serviceAreas.city', 'city')
      .leftJoinAndSelect('serviceAreas.state', 'state')
      .leftJoinAndSelect('marketCenter.account', 'account')
      .where('marketCenter.id = :id', { id })
      .andWhere('marketCenter.isDeleted = false')
      .andWhere('marketCenterUsers.isDeleted = false')
      .andWhere('userRole.isActive = true')
      .select([
        'marketCenter.id',
        'marketCenter.name',
        'marketCenter.code',
        'marketCenter.phoneNumber',
        'marketCenter.isActive',
        'marketCenter.createdAt',
        'marketCenter.updatedAt',
        'address.id',
        'address.street',
        'address.city',
        'address.state',
        'address.zipcode',
        'marketCenterUsers.id',
        'marketCenterUsers.isDeleted',
        'marketCenterUsers.isActive',
        'marketCenterUsers.createdAt',
        'marketCenterUsers.updatedAt',
        'userRole.id',
        'userRole.isActive',
        'user.id',
        'user.firstName',
        'user.lastName',
        'user.contactNumber',
        'user.isActive',
        'user.username',
        'user.email',
        'user.profileImageUrl',
        'role.id',
        'role.name',
        'serviceAreas.id',
        'serviceAreas.zipCode',
        'city.id',
        'city.cityName',
        'state.id',
        'state.stateName',
        'account.id',
        'account.name',
        'allUserRoles.id',
        'allRoles.id',
        'allRoles.name',
      ])
      .getOne();

    if (!marketCenter) {
      throw new NotFoundException(`Market center with ID ${id} not found`);
    }

    const { account, ...restMarketCenter } = marketCenter;
    const transformedMarketCenter = {
      ...restMarketCenter,
      company: {
        id: account.id,
        name: account.name,
      },
      marketCenterUsers: marketCenter.marketCenterUsers.map((mcu) => {
        const userRoles = [
          ...new Set(
            mcu.userRole.user.userRoles
              .filter((ur) => ur.role?.name)
              .map((ur) => ur.role.name),
          ),
        ];

        return {
          id: mcu.id,
          isActive: mcu.isActive,
          isDeleted: mcu.isDeleted,
          createdAt: mcu.createdAt,
          updatedAt: mcu.updatedAt,
          user: mcu.userRole.user,
          role: mcu.userRole.role,
          roles: userRoles,
          hasMultipleRoles: userRoles.length > 1,
        };
      }),
    };

    return transformedMarketCenter;
  }

  async changeMarketCenterManagerStatus(
    id: string,
    isActive: boolean,
    marketCenterId: string,
  ): Promise<MarketCenterUser> {
    return this.manager.transaction(async (transactionalEntityManager) => {
      // First check if market center exists
      const marketCenter = await transactionalEntityManager.findOne(
        MarketCenter,
        {
          where: {
            id: marketCenterId,
            isDeleted: false,
          },
        },
      );

      if (!marketCenter) {
        throw new NotFoundException('Market center not found');
      }

      // Find the market center user with account user information
      const marketCenterUser = await transactionalEntityManager
        .createQueryBuilder(MarketCenterUser, 'mcu')
        .leftJoinAndSelect('mcu.marketCenter', 'marketCenter')
        .leftJoinAndSelect('mcu.userRole', 'userRole')
        .leftJoinAndSelect('userRole.user', 'user')
        .leftJoinAndSelect('userRole.role', 'role')
        .where('userRole.user.id = :id', { id })
        .andWhere('marketCenter.id = :marketCenterId', { marketCenterId })
        .andWhere('role.name = :roleName', {
          roleName: 'market_center_manager',
        })
        .andWhere('mcu.isDeleted = false')
        .getOne();

      if (!marketCenterUser) {
        throw new NotFoundException('Market center manager not found');
      }
      // Update market center user status
      const updatedMarketCenterUser = await transactionalEntityManager.save(
        MarketCenterUser,
        {
          ...marketCenterUser,
          isActive,
        },
      );

      return updatedMarketCenterUser;
    });
  }
}
