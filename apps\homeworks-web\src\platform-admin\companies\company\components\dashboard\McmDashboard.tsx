import DashboardHeader from "./DashboardHeader";
import { SummaryCard } from "./SummaryCard";
import DonutChartCard from "./DonutChartCard";
import TopMarketCenterTable  from "./TopMarketCenterTable";
import TopAgentTable from "./TopAgentTable";
import {DollarSign} from 'lucide-react'
import { ReactComponent as SPDashboardIcon } from '../../../../../assets/icons/SPDashboardIcon.svg';
import { ReactComponent as MCMDashboard } from '../../../../../assets/icons/MCMDashboard.svg';
import { ReactComponent as OwnerDashboard } from '../../../../../assets/icons/OwnerDashboard.svg';
import { ReactComponent as PropertiesIcon } from '../../../../../assets/icons/PropertiesIcon.svg';
import { ReactComponent as AgentDashboardIcon } from '../../../../../assets/icons/AgentDashboardIcon.svg';
import { ReactComponent as HomeDiary } from '../../../../../assets/icons/HomeDiary.svg';
import { ReactComponent as JobsDashboardIcon } from '../../../../../assets/icons/JobsDashboardIcon.svg';
import { ReactComponent as RevenueDashboard } from '../../../../../assets/icons/RevenueDashboard.svg';
import { ReactComponent as CommissionIcon } from '../../../../../assets/icons/CommissionIcon.svg';
import { CategoryWiseRevenueChart } from "./CategoryWiseCHart";



export default function Dashboard() {
  return (
    <div className="h-[100%] relative overflow-y-auto scrollbar">
      <div className="p-6 space-y-2">
        <DashboardHeader />
        {/* Summary Cards */}
        <div className="grid grid-cols-3 gap-6">
          <SummaryCard
            title="Total Properties"
            value="600"
            icon={<PropertiesIcon />}
            amountClass="px-10 text-[20px] font-semibold"
            containerClass="h-[90px] bg-[#EBCFCF] xl:max-w-[346px] 2xl:max-w-[535px] p-2"
          />
          <SummaryCard
            title="Agents"
            value="150"
            icon={<AgentDashboardIcon />}
            amountClass="px-10 text-[20px] font-semibold"
            containerClass="h-[90px] bg-[#B2E0F4] xl:max-w-[350px] 2xl:max-w-[535px] "
          />
          <SummaryCard
            title="Total Revenue"
            value="300"
            icon={<CommissionIcon />}
            amountClass="px-10 text-[20px] font-semibold"
            containerClass="h-[90px] bg-[#DCDADA] xl:max-w-[350px] 2xl:max-w-[535px]"
          />
        </div>
        {/* Donut Charts */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Column 1 */}
          <div className="bg-white rounded-xl border overflow-hidden shadow-sm flex flex-col gap-2 h-full">
            <SummaryCard
              title="Jobs"
              value="420"
              icon={<JobsDashboardIcon />}
              amountClass="px-10 text-[20px] font-semibold"
              containerClass="h-[80px] bg-[#0000FE0F] 2xl:max-w-[535px]"
            />
            <DonutChartCard
              data={[
                { name: 'Active', value: 130, color: '#9C8FFF' },
                { name: 'Pending', value: 70, color: '#FFA5A5' },
                { name: 'Complete', value: 220, color: '#54D387' },
              ]}
            />
          </div>

          {/* Column 2 */}
          <div className="bg-white rounded-xl border overflow-hidden shadow-sm flex flex-col gap-2 h-full">
            <SummaryCard
              title="Revenue by Categories"
              value="$25,000"
              icon={<RevenueDashboard />}
              amountClass="px-10 text-[20px] font-semibold"
              containerClass="h-[80px] bg-[#E1EFC8] 2xl:max-w-[535px]"
            />
            <DonutChartCard
              data={[
                { name: 'Plumbing', value: 12960, color: '#60a5fa' },
                { name: 'Cleaning', value: 7920, color: '#fbbf24' },
                { name: 'Rest All', value: 5120, color: '#34d399' },
              ]}
            />
          </div>

          {/* Column 3 */}
          <div className="bg-white rounded-xl border overflow-hidden shadow-sm flex flex-col gap-2 h-full">
            <SummaryCard
              title="Total Commission"
              value="$12,200"
              icon={<CommissionIcon />}
              amountClass="px-10 text-[20px] font-semibold"
              containerClass="h-[80px] bg-[#FFE8BD] 2xl:max-w-[535px]"
            />
            <DonutChartCard
              data={[
                { name: 'Market Centres', value: 4320, color: '#818cf8' },
                { name: 'Agents', value: 2880, color: '#f472b6' },
                { name: 'Company', value: 5000, color: '#F3E8FF' },
              ]}
            />
          </div>
        </div>
        <CategoryWiseRevenueChart/>
        {/* Tables */}
        {/* <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <TopMarketCenterTable />
          <TopAgentTable />
        </div> */}
      </div>
    </div>
  );
}
