
import { DataTable } from '../../../../../components/table/data-table';
import { useState } from "react";
import { ColumnDef } from "@tanstack/react-table";

const emptyData: any[] = [];

const getColumns = (): ColumnDef<any>[] => [
  {
    header: "S.No.",
    cell: ({ row }) => row.index + 1,
  },
  {
    accessorKey: "name",
    header: "Market Centre Name",
  },
  {
    accessorKey: "jobs",
    header: "Jobs",
  },
  {
    accessorKey: "revenue",
    header: "Revenue",
  },
  {
    accessorKey: "commission",
    header: "Commission",
    cell: ({ row }) => <span className="font-semibold">{row.original.commission}</span>,
  },
  {
    accessorKey: "commission",
    header: "%",
    cell: ({ row }) => <span className="font-semibold">{row.original.commission}</span>,
  },
];

const mockData = [
  { name: "Alabama – Selma", region: "South East", jobs: 30, revenue: "$3,600", commission: "38%" },
  { name: "Arizona – Globe", region: "South West", jobs: 24, revenue: "$2,400", commission: "30%" },
  { name: "California – Arcadia", region: "West", jobs: 18, revenue: "$2,000", commission: "27%" },
  { name: "Florida – Bartow", region: "South East", jobs: 15, revenue: "$1,800", commission: "18%" },
  { name: "New York – Albany", region: "North East", jobs: 10, revenue: "$1,200", commission: "12%" },
];

export default function TopMarketCenterTable() {
  const [query, setQuery] = useState({});
  const [rowSelection, setRowSelection] = useState({});

  return (
    <DataTable
      data={mockData || emptyData}
      columns={getColumns()}
      query={query}
      setQuery={setQuery}
      rowSelection={rowSelection}
      setRowSelection={setRowSelection}
      isRowSelection={false}
      uniqueField="name"
      numbered={true}
      paginationType="client-side"
      tableContainerClassName="rounded-md"
      isLoading={false}
    />
  );
}
