import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateServiceProviderServiceDto } from '../dto/create_service_provider_service.dto';
import { ServiceProviderServiceRepository } from '../repositories/service-provider-service-repository/service-provider-service.repository';
import { UpdateServiceProviderServiceDto } from '../dto/update-service-provider-service.dto';
import { ActivateDeactivateServiceProviderServiceDto } from '../dto/active-deactive-service-provider-service.dto';

@Injectable()
export class ServiceProviderServiceService {
  constructor(
    private readonly serviceProviderServiceRepository: ServiceProviderServiceRepository,
  ) {}

  async createServiceProviderService(
    createServiceProviderServiceDto: CreateServiceProviderServiceDto,
  ) {
    const newlyAddedService =
      await this.serviceProviderServiceRepository.createServiceProviderService(
        createServiceProviderServiceDto,
      );

    return {
      data: newlyAddedService,
      message: `Service added successfully`,
    };
  }

  async updateServiceProviderService(
    id: string,
    updateDto: UpdateServiceProviderServiceDto,
  ) {
    const serviceProviderService =
      await this.serviceProviderServiceRepository.findOneById(id);
    if (!serviceProviderService) {
      throw new NotFoundException(
        `Service provider service with ID ${id} not found`,
      );
    }
    const updateService =
      await this.serviceProviderServiceRepository.updateServiceProviderService(
        id,
        updateDto,
      );

    return {
      data: updateService,
      message: 'Service updated successfully',
    };
  }

  async changeServiceProviderServiceStatus(
    id: string,
    { isActive }: ActivateDeactivateServiceProviderServiceDto,
  ) {
    const updatedService =
      await this.serviceProviderServiceRepository.changeServiceStatus(
        id,
        isActive,
      );

    return {
      data: updatedService,
      message: `Service ${isActive ? 'activated' : 'deactivated'} successfully`,
    };
  }
}
