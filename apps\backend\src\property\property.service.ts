import {
  Injectable,
  NotFoundException,
  Logger,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, In, Not, Brackets } from 'typeorm';
import { Property } from './entities/property.entity';
import { User } from '../users/entities/user.entity';
import { Address } from '../users/entities/address.entity';
import { CreatePropertyDto } from './dto/property.dto';
import { PropertyOwner } from './entities/property-owner.entity';
import { UnitType } from './entities/unit-type.entity';
import { CodeGeneratorService } from '../../../../libs/common/src';
import { PropertyApprovalStatus } from './entities/property-approval-status.entity';
import { SortSearchDataDto } from '../common/pagination/sort-search-data.dto';
import { Response } from 'express';
import * as Excel from 'exceljs';
import { UserRole } from '../role/entities/user-role.entity';
import { Role } from '../role/entities/role.entity';
import { EmailService } from '../common/email/email.service';
import { MarketCenterUser } from '../market-centers/entities/market-center-user.entity';
import { CommonService } from '../common/utils/common.service';
import { MarketCenter } from '../market-centers/entities/market-center.entity';
import { UpdatePropertyDto } from './dto/update-property.dto';
import { AccountUser } from '../accountusers/entities/account.user.entity';
import { ROLES_ENUM } from '../../../backend/src/auth/enum/roles.enum';
import { PropertyRepository } from './property.repository';
import { ZipCode } from '../zip-codes/entities/zip-code.entity';
import { AgentDetail } from '../agent/entities/agent.entity';
import { UpdatePropertyApprovalStatusDto } from './dto/updatePropertyApprovalStatusDto';

@Injectable()
export class PropertyService {
  logger: any;
  constructor(
    @InjectRepository(Property)
    private propertyRepository: Repository<Property>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Address)
    private addressRepository: Repository<Address>,
    @InjectRepository(PropertyOwner)
    private propertyOwnerRepository: Repository<PropertyOwner>,
    @InjectRepository(UnitType)
    private unitTypeRepository: Repository<UnitType>,
    @InjectRepository(PropertyApprovalStatus)
    private propertyApprovalStatusRepository: Repository<PropertyApprovalStatus>,
    private dataSource: DataSource,
    private codeGeneratorService: CodeGeneratorService,
    @InjectRepository(UserRole)
    private userRoleRepository: Repository<UserRole>,
    private emailService: EmailService,
    @InjectRepository(MarketCenterUser)
    private marketCenterUserRepository: Repository<MarketCenterUser>,
    private commonService: CommonService,
    @InjectRepository(MarketCenter)
    private marketCenterRepository: Repository<MarketCenter>,
    private readonly propertyRepositoryy: PropertyRepository,
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
    @InjectRepository(AccountUser)
    private accountUserRepository: Repository<AccountUser>,
    @InjectRepository(AgentDetail)
    private agentRepository: Repository<AgentDetail>,
  ) {
    this.logger = new Logger(PropertyService.name);
  }

  async createPropertyForUser(
    createPropertyDto: CreatePropertyDto,
  ): Promise<any> {
    // Start a transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Generate unique property code
      const propertyCode = await this.codeGeneratorService.generateUniqueCode({
        type: 'alphanumeric',
        prefix: 'P',
        length: 7,
        repository: this.propertyRepository,
      });
      const accountId = createPropertyDto?.accountId;
      const marketCenterId = createPropertyDto?.marketCenterId;

      let accountToUse = accountId;

      if (marketCenterId && !accountId) {
        const marketCenter = await this.marketCenterRepository.findOne({
          where: { id: marketCenterId },
          select: ['account'], // assuming account is a relation
        });

        if (marketCenter) {
          accountToUse = marketCenter?.account?.id
            ? marketCenter.account.id
            : null; // handle relation or raw field
        }
      }
      // Find the unit type by ID if provided
      let unitType = null;
      if (
        createPropertyDto.propertyDetails.unit_type &&
        (createPropertyDto.roleId == '1' || createPropertyDto.roleId == '2')
      ) {
        unitType = await this.unitTypeRepository.findOne({
          where: [
            isNaN(+createPropertyDto.propertyDetails.unit_type)
              ? { name: createPropertyDto.propertyDetails.unit_type }
              : { id: createPropertyDto.propertyDetails.unit_type },
          ],
        });

        if (!unitType) {
          throw new NotFoundException(
            `Unit type with ID ${createPropertyDto.propertyDetails.unit_type} not found`,
          );
        }
      } else {
        unitType = await this.unitTypeRepository.findOne({
          where: [
            isNaN(+createPropertyDto.propertyDetails.unit_type)
              ? { name: createPropertyDto.propertyDetails.unit_type }
              : { id: createPropertyDto.propertyDetails.unit_type },
          ],
        });

        // TODO:: for now this condition will fix the error of home-type issue, this needs to be fixed and use better approach

        if (!unitType) {
          throw new NotFoundException(
            `Unit type with ID ${createPropertyDto.propertyDetails.unit_type} not found`,
          );
        }
      }
      // Check if owner details are provided and valid
      if (
        createPropertyDto.owner &&
        createPropertyDto.owner.email &&
        createPropertyDto.owner.email.trim().length > 0
      ) {
        // Check if user exists by email
        let user = await this.userRepository.findOne({
          where: {
            username: createPropertyDto.owner.email.toLowerCase(),
            isDeleted: false,
          },
        });

        // Flag to track if this is a new user
        const isNewUser = !user;
        let generatedPassword = null;

        // If user exists, check if they already own a property
        let existingOwnership = null;
        if (user) {
          existingOwnership = await this.propertyOwnerRepository.findOne({
            where: { ownerId: user.id, isActive: true },
          });
          if (existingOwnership) {
            this.logger.log(`User ${user.email} already owns a property`);
          }
          //update the user object with new address and contact details
          user.firstName = createPropertyDto.owner.firstName || user.firstName;
          user.lastName = createPropertyDto.owner.lastName || user.lastName;
          user.contactNumber =
            createPropertyDto.owner.phoneNumber || user.contactNumber;

          await queryRunner.manager.save(user);
          // if (existingOwnership) {
          //   userAlreadyOwnsProperty = true;
          //   // Instead of throwing an error, return a warning message
          //   await queryRunner.rollbackTransaction();
          //   return {
          //     warning: true,
          //     message: 'This user already owns a property. Please use a different email.',
          //     existingOwnership: true
          //   };
          // }
        } else {
          // Generate secure password for new user

          // Create new user if doesn't exist
          user = this.userRepository.create({
            email: createPropertyDto.owner.email.toLowerCase(),
            username:
              createPropertyDto.owner.username?.toLowerCase() ||
              createPropertyDto.owner.email.toLowerCase(),
            firstName: createPropertyDto.owner.firstName || '',
            lastName: createPropertyDto.owner.lastName || '',
            contactNumber: createPropertyDto.owner.phoneNumber || '',
            isActive: true,
            isDeleted: false,
          });

          user = await queryRunner.manager.save(user);
        }
        // Update user password if it's a new user

        // Check if user has the "owner" role, if not, add it
        const ownerRole = await queryRunner.manager.findOne(Role, {
          where: { name: ROLES_ENUM.OWNER },
        });

        if (!ownerRole) {
          this.logger.warn('Owner role not found in the system');
        } else {
          // Check if user already has the owner role
          const existingUserRole = await queryRunner.manager.findOne(UserRole, {
            where: {
              userId: user.id,
              roleId: ownerRole.id,
              isDeleted: false,
            },
          });

          if (!existingUserRole) {
            // Create new user role
            const newUserRole = queryRunner.manager.create(UserRole, {
              userId: user.id,
              roleId: ownerRole.id,
              isActive: true,
              isDeleted: false,
            });

            await queryRunner.manager.save(newUserRole);
            this.logger.log(`Added owner role to user ${user.email}`);
          }
        }

        const zipCodeDetails = await queryRunner.manager.findOneBy(ZipCode, {
          zipCode: createPropertyDto.propertyAddress.zipcode,
        });

        if (!zipCodeDetails) {
          throw new BadRequestException('No zip code found');
        }

        // Create address for the property
        const address = queryRunner.manager.create(Address, {
          street: createPropertyDto.propertyAddress.street,
          city: createPropertyDto.propertyAddress.city,
          state: createPropertyDto.propertyAddress.state,
          zipcode: createPropertyDto.propertyAddress.zipcode,
          zipCode: { id: zipCodeDetails.id }, // Add zipId field
        });

        const savedAddress = await queryRunner.manager.save(address);

        // Create owner address if provided
        let ownerAddress = null;
        if (createPropertyDto.owner.address) {
          // Check if owner address is the same as property address
          const isSameAddress =
            createPropertyDto.owner.address.street ===
              createPropertyDto.propertyAddress.street &&
            createPropertyDto.owner.address.city ===
              createPropertyDto.propertyAddress.city &&
            createPropertyDto.owner.address.state ===
              createPropertyDto.propertyAddress.state &&
            createPropertyDto.owner.address.zipcode ===
              createPropertyDto.propertyAddress.zipcode;

          if (isSameAddress) {
            // Use the same address that was already created for the property
            ownerAddress = savedAddress;
          } else {
            // Create a new address for the owner
            ownerAddress = queryRunner.manager.create(Address, {
              street: createPropertyDto.owner.address.street,
              city: createPropertyDto.owner.address.city,
              state: createPropertyDto.owner.address.state,
              zipcode: createPropertyDto.owner.address.zipcode,
            });

            ownerAddress = await queryRunner.manager.save(ownerAddress);
          }
        }

        // Add this before creating the property
        let creator = null;
        if (createPropertyDto.createdBy) {
          creator = await this.userRepository.findOne({
            where: { id: String(createPropertyDto.createdBy) },
          });
        }
        let logginRole: Role;
        let propertyStatus;

        if (createPropertyDto?.roleId) {
          logginRole = await this.roleRepository.findOne({
            where: { id: createPropertyDto?.roleId },
          });
        }
        // Get the property status (default to PENDING if not specified)
        if (
          (logginRole.name == ROLES_ENUM.MARKET_CENTER_MANAGER ||
            logginRole.name == ROLES_ENUM.COMPANY_ADMIN) &&
          createPropertyDto.owner?.email
        ) {
          propertyStatus = await this.propertyApprovalStatusRepository.findOne({
            where: { name: 'PENDING FROM OWNER' }, // Default status
          });
        }

        if (
          ((logginRole.name == ROLES_ENUM.MARKET_CENTER_MANAGER ||
            logginRole.name == ROLES_ENUM.COMPANY_ADMIN) &&
            (!createPropertyDto.owner?.email ||
              (createPropertyDto?.owner?.email && !existingOwnership))) ||
          createPropertyDto?.email
        ) {
          propertyStatus = await this.propertyApprovalStatusRepository.findOne({
            where: { name: 'APPROVED' }, // Default status
          });
        }

        if (logginRole.name == ROLES_ENUM.AGENT) {
          propertyStatus = await this.propertyApprovalStatusRepository.findOne({
            where: { name: 'PENDING FROM MCM' }, // Default status
          });
        }

        // Find userRole if userRoleId is provided
        let userRole = null;
        if (createPropertyDto.roleId) {
          userRole = await this.userRoleRepository.findOne({
            where: {
              roleId: createPropertyDto.roleId,
              userId: createPropertyDto.createdBy,
            },
          });

          if (!userRole) {
            throw new NotFoundException(`UserRole not found`);
          }
        }

        // Create property
        const generateProperty = this.propertyRepository.create({
          unit_type: unitType,
          unit_name: createPropertyDto.propertyDetails.unit_name,
          building_name: createPropertyDto.propertyDetails.building_name,
          floor_count: createPropertyDto.propertyDetails.floor_count
            ? Number(createPropertyDto.propertyDetails.floor_count)
            : null,
          rooms_count: createPropertyDto.propertyDetails.rooms_count
            ? Number(createPropertyDto.propertyDetails.rooms_count)
            : null,
          bed_count: createPropertyDto.propertyDetails.bed_count
            ? Number(createPropertyDto.propertyDetails.bed_count)
            : null,
          full_bath_count: createPropertyDto.propertyDetails.full_bath_count
            ? Number(createPropertyDto.propertyDetails.full_bath_count)
            : null,
          half_bath_count: createPropertyDto.propertyDetails.half_bath_count
            ? Number(createPropertyDto.propertyDetails.half_bath_count)
            : null,
          three_fourth_bath_count: createPropertyDto.propertyDetails
            .three_fourth_bath_count
            ? Number(createPropertyDto.propertyDetails.three_fourth_bath_count)
            : null,
          one_fourth_bath_count: createPropertyDto.propertyDetails
            .one_fourth_bath_count
            ? Number(createPropertyDto.propertyDetails.one_fourth_bath_count)
            : null,
          lot_size: createPropertyDto.propertyDetails.lot_size
            ? Number(createPropertyDto.propertyDetails.lot_size)
            : null,
          description: createPropertyDto.propertyDetails.description,
          tax_id: createPropertyDto.propertyDetails.tax_id,
          year_of_built: createPropertyDto.propertyDetails.year_of_built,
          finished_size: createPropertyDto.propertyDetails.finished_size
            ? Number(createPropertyDto.propertyDetails.finished_size)
            : null,
          garage_size: createPropertyDto.propertyDetails.garage_size
            ? Number(createPropertyDto.propertyDetails.garage_size)
            : null,
          basement_size: createPropertyDto.propertyDetails.basement_size
            ? Number(createPropertyDto.propertyDetails.basement_size)
            : null,
          statusId: propertyStatus.id,
          status: propertyStatus, // Include the full status entity
          addressId: savedAddress.id,
          createdBy: createPropertyDto.createdBy
            ? String(createPropertyDto.createdBy)
            : null,
          updatedBy: createPropertyDto.createdBy
            ? String(createPropertyDto.createdBy)
            : null,
          userRole: userRole,
          userRoleId: userRole?.id,
          code: propertyCode ? propertyCode : '',
          account: { id: accountToUse },
          marketCenter: marketCenterId ? { id: marketCenterId } : null,
        });

        const savedProperty = await queryRunner.manager.save(generateProperty);

        // Create property owner relationship
        const propertyOwner = queryRunner.manager.create(PropertyOwner, {
          ownerId: user.id,
          propertyId: savedProperty.id,
          isActive: true,
          ownershipStartDate: new Date(),
          addressId: ownerAddress ? ownerAddress.id : null, // Add owner address to property owner
        });

        await queryRunner.manager.save(propertyOwner);

        // Commit transaction
        await queryRunner.commitTransaction();

        // Get market center info if the creator is a market center manager
        let marketCenterName = 'HomeWorks';
        let creatorName = 'HomeWorks Admin';

        if (createPropertyDto.createdBy && createPropertyDto.roleId) {
          const creator = await this.userRepository.findOne({
            where: { id: String(createPropertyDto.createdBy) },
          });

          if (creator) {
            creatorName = `${creator.firstName || ''} ${
              creator.lastName || ''
            }`.trim();

            const role = await this.roleRepository.findOne({
              where: { id: createPropertyDto.roleId },
            });

            if (role?.name === ROLES_ENUM.MARKET_CENTER_MANAGER) {
              const userRole = await this.userRoleRepository.findOne({
                where: {
                  roleId: createPropertyDto.roleId,
                  userId: createPropertyDto.createdBy,
                },
              });
              const marketCenterUser =
                await this.marketCenterUserRepository.findOne({
                  where: {
                    userRole: { id: userRole.id },
                  },
                  relations: ['marketCenter', 'userRole'],
                });

              if (marketCenterUser?.marketCenter?.name) {
                marketCenterName = marketCenterUser.marketCenter.name;
              }
            } else if (role?.name === ROLES_ENUM.COMPANY_ADMIN) {
              const userRole = await this.userRoleRepository.findOne({
                where: {
                  roleId: createPropertyDto.roleId,
                  userId: createPropertyDto.createdBy,
                },
              });
              const companyAdmin = await this.accountUserRepository.findOne({
                where: { userRoleId: userRole.id },
                relations: ['account'],
              });

              // if (companyAdmin?.userRoles?.name) {
              //   marketCenterName = companyAdmin.account.name;
              // }
            }
          }
        }

        // Send appropriate email based on whether this is a new user or existing user
        try {
          // Get property address for email
          const propertyAddressStr = `${savedAddress.street}, ${savedAddress.city}, ${savedAddress.state} ${savedAddress.zipcode}`;

          // Get creator name for email
          const creatorName = creator
            ? `${creator.firstName || ''} ${creator.lastName || ''}`.trim()
            : 'HomeWorks Admin';

          if (
            createPropertyDto.roleId == '5' ||
            createPropertyDto.roleId == '6'
          ) {
            if (isNewUser || !existingOwnership) {
              const { password, hashedPassword } =
                await this.commonService.generateSecurePassword();
              generatedPassword = password;

              if (generatedPassword) {
                // Update the user's password if it's a new user
                await this.userRepository.update(user.id, {
                  password: hashedPassword,
                });
              }
              // Send welcome email to new owner with password
              await this.sendNewOwnerWelcomeEmail(
                user.email,
                user.firstName,
                user.lastName,
                user.username,
                user.contactNumber,
                creatorName,
                marketCenterName,
                generatedPassword, // Pass the generated password
                propertyAddressStr,
                creator?.email,
                creator?.contactNumber,
                // Pass the property address
              );
            } else {
              // Check if we need to generate a password for existing user without password

              // Send property added email to existing owner
              await this.sendPropertyAddedEmailForExistingUser(
                user.email,
                user.firstName,
                user.lastName,
                user.username,
                user.contactNumber,
                creatorName,
                marketCenterName,
                // Pass the generated password if it was created
                propertyAddressStr, // Pass the property address
              );
            }
          }
        } catch (emailError) {
          // Log email error but don't fail the transaction
          this.logger.error(
            `Failed to send email to owner: ${emailError.message}`,
            emailError.stack,
          );
        }

        return {
          savedProperty,
          message: 'Property saved successfully',
        };
      } else {
        const zipCodeDetails = await queryRunner.manager.findOneBy(ZipCode, {
          zipCode: createPropertyDto.propertyAddress.zipcode,
        });
        // Handle case where no owner details are provided
        const address = this.addressRepository.create({
          street: createPropertyDto.propertyAddress.street,
          city: createPropertyDto.propertyAddress.city,
          state: createPropertyDto.propertyAddress.state,
          zipcode: createPropertyDto.propertyAddress.zipcode,
          zipCode: { id: zipCodeDetails?.id },
        });

        const savedAddress = await this.addressRepository.save(address);

        // Get the property status (default to PENDING if not specified)
        let propertyStatus =
          await this.propertyApprovalStatusRepository.findOne({
            where: { name: 'APPROVED' }, // Default status
          });

        // Find userRole if userRoleId is provided
        let userRole = null;

        if (createPropertyDto?.roleId) {
          userRole = await this.userRoleRepository.findOne({
            where: {
              roleId: createPropertyDto.roleId,
              userId: createPropertyDto.createdBy,
            },
            relations: ['role'],
          });

          if (!userRole) {
            throw new NotFoundException(`UserRole with  not found`);
          }
        }
        if (userRole?.role?.name && userRole?.role?.name === ROLES_ENUM.AGENT) {
          propertyStatus = await this.propertyApprovalStatusRepository.findOne({
            where: { name: 'PENDING FROM MCM' },
          });
        }
        // Create property without owner
        const generateProperty = this.propertyRepository.create({
          unit_type: unitType,
          unit_name: createPropertyDto.propertyDetails.unit_name,
          building_name: createPropertyDto.propertyDetails.building_name,
          floor_count: createPropertyDto.propertyDetails.floor_count
            ? Number(createPropertyDto.propertyDetails.floor_count)
            : null,
          rooms_count: createPropertyDto.propertyDetails.rooms_count
            ? Number(createPropertyDto.propertyDetails.rooms_count)
            : null,
          bed_count: createPropertyDto.propertyDetails.bed_count
            ? Number(createPropertyDto.propertyDetails.bed_count)
            : null,
          full_bath_count: createPropertyDto.propertyDetails.full_bath_count
            ? Number(createPropertyDto.propertyDetails.full_bath_count)
            : null,
          half_bath_count: createPropertyDto.propertyDetails.half_bath_count
            ? Number(createPropertyDto.propertyDetails.half_bath_count)
            : null,
          three_fourth_bath_count: createPropertyDto.propertyDetails
            .three_fourth_bath_count
            ? Number(createPropertyDto.propertyDetails.three_fourth_bath_count)
            : null,
          one_fourth_bath_count: createPropertyDto.propertyDetails
            .one_fourth_bath_count
            ? Number(createPropertyDto.propertyDetails.one_fourth_bath_count)
            : null,
          lot_size: createPropertyDto.propertyDetails.lot_size
            ? Number(createPropertyDto.propertyDetails.lot_size)
            : null,
          description: createPropertyDto.propertyDetails.description,
          tax_id: createPropertyDto.propertyDetails.tax_id,
          year_of_built: createPropertyDto.propertyDetails.year_of_built,
          finished_size: createPropertyDto.propertyDetails.finished_size
            ? Number(createPropertyDto.propertyDetails.finished_size)
            : null,
          garage_size: createPropertyDto.propertyDetails.garage_size
            ? Number(createPropertyDto.propertyDetails.garage_size)
            : null,
          basement_size: createPropertyDto.propertyDetails.basement_size
            ? Number(createPropertyDto.propertyDetails.basement_size)
            : null,
          statusId: propertyStatus.id,
          status: propertyStatus, // Include the full status entity
          addressId: savedAddress.id,
          createdBy: createPropertyDto.createdBy
            ? String(createPropertyDto.createdBy)
            : null,
          updatedBy: createPropertyDto.createdBy
            ? String(createPropertyDto.createdBy)
            : null,
          userRole: userRole,
          userRoleId: userRole?.id,
          code: propertyCode ? propertyCode : '',
          account: { id: accountToUse },
          marketCenter: marketCenterId ? { id: marketCenterId } : null,
        });

        const savedProperty = await this.propertyRepository.save(
          generateProperty,
        );

        if (createPropertyDto?.email) {
          const _user = await this.userRepository.findOneBy({
            username: createPropertyDto.email,
          });

          const ownerRole = await this.roleRepository.findOneBy({
            name: ROLES_ENUM.OWNER,
          });

          let userRole;

          userRole = await this.userRoleRepository.findOneBy({
            roleId: ownerRole.id,
            userId: _user.id,
          });

          if (!userRole) {
            userRole = queryRunner.manager.create(UserRole, {
              user: _user,
              userId: _user.id,
              role: ownerRole,
              roleId: ownerRole.id,
            });
            await queryRunner.manager.save(userRole);
          }
          await queryRunner.manager.update(Property, savedProperty.id, {
            userRole: userRole,
            userRoleId: userRole.id,
            createdBy: _user.id,
          });
          const propertyOwner = queryRunner.manager.create(PropertyOwner, {
            ownerId: _user.id,
            propertyId: savedProperty.id,
            isActive: true,
            ownershipStartDate: new Date(),
            addressId: savedAddress.id, // Add owner address to property owner
          });

          await queryRunner.manager.save(propertyOwner);

          await queryRunner.commitTransaction();
        }

        return {
          savedProperty,
          message: 'Property saved successfully',
        };
      }
    } catch (error) {
      // Rollback transaction on error
      await queryRunner.rollbackTransaction();
      this.logger.error(
        `Error creating property: ${error.message}`,
        error.stack,
      );
      throw error;
    } finally {
      // Release query runner
      await queryRunner.release();
    }
  }

  // New method to send welcome email to new owners
  async sendNewOwnerWelcomeEmail(
    email: string,
    firstName: string,
    lastName: string,
    emailAddress: string,
    contactNumber: string,
    marketCenterManagerName: string,
    marketCenterName: string,
    password: string, // Add password parameter
    propertyAddress?: string,
    creatorEmail?: string,
    creatorPhoneNumber?: string,
    // Add property address parameter
  ): Promise<void> {
    try {
      const fullName =
        `${firstName || ''} ${lastName || ''}`.trim() || 'Property Owner';

      // Generate email using the email service
      await this.emailService.generateAndSendEmail({
        to: email,
        subject: 'Welcome to HomeWorks - Access Your Mobile App Account',
        templateName: 'MAIL_FOR_NEW_OWNER',
        templateData: {
          propertyOwnerName: fullName,
          emailAddress: emailAddress,
          phoneNumber: contactNumber,
          marketCenterManagerName: marketCenterManagerName,
          marketCenterName: marketCenterName,
          temporaryPassword: password,
          userEmail: email, // Include password in email
          propertyAddress: propertyAddress,
          senderEmail: creatorEmail,
          senderPhoneNumber: creatorPhoneNumber, // Include property address
          appDownloadLink:
            process.env.VITE_AGENT_LOGIN_URL ||
            'https://play.google.com/store/apps/details?id=com.yourcompany.homeworks',
        },
      });

      this.logger.log(`Welcome email sent successfully to new owner: ${email}`);
    } catch (error) {
      this.logger.error(
        `Failed to send welcome email to owner: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  // New method to send property added email to existing owners
  async sendPropertyAddedEmailForExistingUser(
    email: string,
    firstName: string,
    lastName: string,
    emailAddress: string,
    contactNumber: string,
    creatorName: string,
    marketCenterName: string,
    password?: string, // Add optional password parameter
    propertyAddress?: string, // Add optional property address parameter
  ): Promise<void> {
    try {
      const fullName =
        `${firstName || ''} ${lastName || ''}`.trim() || 'Property Owner';

      // Generate email using the email service
      await this.emailService.generateAndSendEmail({
        to: email,
        subject: `Action Required: Property Association Request from ${marketCenterName}`,
        templateName: 'MAIL_FOR_EXISTING_OWNER',
        templateData: {
          propertyOwnerName: fullName,
          emailAddress: emailAddress,
          phoneNumber: contactNumber,
          marketCenterManagerName: creatorName,
          marketCenterName: marketCenterName,
          temporaryPassword: password,
          userEmail: email,
          // Include password in email if provided
          propertyAddress: propertyAddress, // Include property address if provided
          approveLink:
            process.env.VITE_AGENT_LOGIN_URL ||
            `https://play.google.com/store/apps/details?id=com.yourcompany.homeworks`, // Add login URL
          rejectLink:
            process.env.VITE_AGENT_LOGIN_URL ||
            `https://play.google.com/store/apps/details?id=com.yourcompany.homeworks`, // Add login URL
        },
      });

      this.logger.log(
        `Property added email sent successfully to existing owner: ${email}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to send property added email to owner: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async getPropertyByUserId(userId: string): Promise<any> {
    try {
      const properties = await this.propertyRepository
        .createQueryBuilder('property')
        .leftJoinAndSelect('property.address', 'address') // Join the address table
        .leftJoinAndSelect('property.userRole', 'userRole') // Join the userRole table
        .leftJoinAndSelect('property.status', 'status') // Join the status table
        .leftJoinAndSelect('property.unit_type', 'unitType') // Join the unit type table
        .innerJoin('property_owner', 'po', 'po.propertyId = property.id') // Join the property_owner table
        .where('po.ownerId = :userId', { userId }) // Filter by ownerId
        .select([
          'property.id',
          'property.unit_type',
          'property.code',
          'property.unit_name',
          'property.building_name',
          'property.floor_count',
          'property.rooms_count',
          'property.bed_count',
          'property.full_bath_count',
          'property.half_bath_count',
          'property.three_fourth_bath_count',
          'property.one_fourth_bath_count',
          'property.lot_size',
          'property.description',
          'property.year_of_built',
          'property.finished_size',
          'property.garage_size',
          'property.basement_size',
          'property.status',
          'address.id',
          'address.street',
          'address.city',
          'address.state',
          'address.zipCode',
          'address.zipcode',
        ])
        .getMany();

      if (!properties.length) {
        throw new NotFoundException(
          `No properties found for user with ID ${userId}`,
        );
      }

      return {
        data: properties,
        meta: {
          itemCount: properties.length,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error fetching properties for user ${userId}: ${error.message}`,
        error,
      );
      throw error;
    }
  }

  async getUnitTypes() {
    try {
      const unitTypes = await this.unitTypeRepository.find({
        order: { name: 'ASC' },
      });
      return { data: unitTypes };
    } catch (error) {
      this.logger.error(`Error fetching unit types: ${error.message}`, error);
      throw error;
    }
  }

  async getAllProperties(
    query?: any,
    sortSearchDataDto?: SortSearchDataDto,
    ids?: string[],
    filters?: {
      searchText: string;
      status?: string;
      homeType?: string | string[];
      city?: string | string[];
      state?: string | string[];
      owner?: string;
      zipcode?: string;
      createdDateRange?: string[];
      propertyDetails?: string;
      addedBy?: string;
    },
    marketCenterId?: string,
    accountId?: string,
  ) {
    try {
      // Extract pagination and search parameters from query
      const {
        page = 1,
        limit = 10,
        sortColumn = 'createdAt',
        sortOrder = 'DESC',
        currentUser,
        currentRole,
      } = query || {};

      // Use filters object if provided, otherwise extract from query
      const {
        status,
        homeType,
        city,
        state,
        owner,
        zipcode,
        createdDateRange,
        propertyDetails,
        addedBy,
        searchText,
      } = filters || query || {};
      if (!currentRole || !currentUser) {
        throw new BadRequestException(
          'User role or user ID missing from request',
        );
      }
      const queryBuilder = this.propertyRepository
        .createQueryBuilder('property')
        .leftJoinAndSelect('property.address', 'address')
        .leftJoinAndSelect('property.unit_type', 'unit_type')
        .leftJoinAndSelect('property.status', 'status')
        .leftJoinAndSelect('property.userRole', 'userRole')
        .leftJoinAndSelect('userRole.user', 'creatorUser')
        .leftJoinAndSelect('userRole.role', 'creatorRole')
        .leftJoinAndSelect(
          'property_owner',
          'po',
          'po.property_id = property.id',
        )
        .leftJoinAndSelect('user', 'owner_user', 'owner_user.id = po.owner_id')
        .leftJoinAndSelect('property.marketCenter', 'marketCenter')
        .leftJoinAndSelect('property.account', 'account');

      // 👇 Collect matching agent userRoleIds
      let agentUserRoleIds: any[] = [];

      // Step 1: Get agents by accountId or marketCenterId
      const agentWhere: any[] = [];
      if (accountId) {
        agentWhere.push({ account: { id: accountId }, isDeleted: false });
      }
      if (marketCenterId) {
        agentWhere.push({
          marketCenter: { id: marketCenterId },
          isDeleted: false,
        });
      }

      if (agentWhere.length > 0) {
        const agents = await this.agentRepository.find({
          where: agentWhere.length > 1 ? agentWhere : agentWhere[0],
          relations: ['user'],
        });

        const agentUserIds = agents.map((agent) => agent.user.id);

        // Step 2: Get agent userRoles
        const agentRoles = await this.roleRepository.find({
          where: { name: ROLES_ENUM.AGENT },
        });

        const agentRoleIds = agentRoles.map((role) => role.id);

        const agentUserRoles = await this.userRoleRepository.find({
          where: {
            userId: In(agentUserIds),
            roleId: In(agentRoleIds),
          },
          select: ['id'],
        });

        agentUserRoleIds = agentUserRoles.map((ur) => ur.id);
      }

      // Step 3: Get current user's role ID (MCM or Admin)
      let currentUserRoleId: string | null = null;
      const currentUserRole = await this.userRoleRepository.findOne({
        where: {
          userId: currentUser,
          roleId: currentRole.id,
          isDeleted: false,
        },
        select: ['id'],
      });

      if (currentUserRole?.id) {
        currentUserRoleId = currentUserRole.id;
      }

      // ✅ Step 4: Combine all filters using OR
      if (currentRole?.name !== ROLES_ENUM.PLATFORM_ADMIN) {
        queryBuilder.andWhere(
          new Brackets((qb) => {
            if (accountId) {
              qb.where('property.account_id = :accountId', { accountId });
            }
            if (marketCenterId) {
              qb.orWhere('property.market_center_id = :marketCenterId', {
                marketCenterId,
              });
            }

            if (agentUserRoleIds.length > 0) {
              qb.orWhere('userRole.id IN (:...agentUserRoleIds)', {
                agentUserRoleIds,
              });
            }

            if (currentUserRoleId) {
              qb.orWhere('userRole.id = :currentUserRoleId', {
                currentUserRoleId,
              });
            }
          }),
        );
      } else {
        this.logger.log(
          'Skipping all filters for property table for platform admin',
        );
      }

      // Apply status filter
      // Apply status filter
      if (status) {
        if (status === 'active') {
          currentRole.name == ROLES_ENUM.PLATFORM_ADMIN
            ? queryBuilder.andWhere('property.isActive = :isActive', {
                isActive: true,
              })
            : queryBuilder
                .andWhere('property.isActive = :isActive', { isActive: true })
                .andWhere('LOWER(status.name) IN (:...statusNames)', {
                  statusNames: ['approved', 'approved from mcm'],
                });
        } else if (status === 'inactive') {
          queryBuilder.andWhere('property.isActive = :isActive', {
            isActive: false,
          });
        } else if (status === 'pending') {
          queryBuilder.andWhere('LOWER(status.name) IN (:...statusNames)', {
            statusNames: ['pending from owner', 'pending from mcm'],
          });
        } else if (status === 'rejected') {
          queryBuilder.andWhere('LOWER(status.name) IN (:...statusNames)', {
            statusNames: ['rejected from owner', 'rejected from mcm'],
          });
        }
      }

      // Apply search filter
      if (searchText) {
        queryBuilder.andWhere(
          '(LOWER(property.code) LIKE LOWER(:searchText) OR ' +
            'LOWER(property.building_name) LIKE LOWER(:searchText) OR ' +
            'LOWER(unit_type.name) LIKE LOWER(:searchText) OR ' +
            'LOWER(property.tax_id) LIKE LOWER(:searchText) OR ' +
            'LOWER(address.street) LIKE LOWER(:searchText) OR ' +
            'LOWER(address.city) LIKE LOWER(:searchText) OR ' +
            'LOWER(address.state) LIKE LOWER(:searchText) OR ' +
            'LOWER(address.zipcode) LIKE LOWER(:searchText)) OR' +
            '(LOWER(owner_user.firstName) LIKE LOWER(:searchText) OR ' +
            'LOWER(owner_user.lastName) LIKE LOWER(:searchText) OR ' +
            'LOWER(owner_user.email) LIKE LOWER(:searchText) OR ' +
            "CONCAT(LOWER(owner_user.firstName), ' ', LOWER(owner_user.lastName)) LIKE LOWER(:searchText))",
          { searchText: `%${searchText}%` },
        );
      }
      // Apply home type (unit_type) filter
      if (homeType) {
        if (Array.isArray(homeType)) {
          queryBuilder.andWhere('unit_type.id IN (:...homeType)', { homeType });
        } else {
          queryBuilder.andWhere('unit_type.id = :homeType', { homeType });
        }
      }

      // Apply city filter
      if (city && (Array.isArray(city) ? city.length > 0 : city)) {
        if (Array.isArray(city)) {
          queryBuilder.andWhere('address.city IN (:...city)', { city });
        } else {
          queryBuilder.andWhere('address.city = :city', { city });
        }
      }

      // Apply state filter
      if (state && (Array.isArray(state) ? state.length > 0 : state)) {
        if (Array.isArray(state)) {
          queryBuilder.andWhere('address.state IN (:...state)', { state });
        } else {
          queryBuilder.andWhere('address.state = :state', { state });
        }
      }

      // Apply zipcode filter
      if (zipcode) {
        queryBuilder.andWhere('address.zipcode = :zipcode', { zipcode });
      }

      // Apply property details filter
      if (propertyDetails) {
        queryBuilder.andWhere(
          '(LOWER(property.unit_name) LIKE LOWER(:propertyDetails) OR ' +
            'LOWER(property.building_name) LIKE LOWER(:propertyDetails) OR ' +
            'LOWER(property.description) LIKE LOWER(:propertyDetails) OR ' +
            'LOWER(property.tax_id) LIKE LOWER(:propertyDetails)) OR ' +
            'LOWER(property.code) LIKE LOWER(:propertyDetails)',
          { propertyDetails: `%${propertyDetails}%` },
        );
      }

      // Apply added by (creator) filter
      if (addedBy) {
        queryBuilder.andWhere(
          '(LOWER(creatorUser.firstName) LIKE LOWER(:addedBy) OR ' +
            'LOWER(creatorUser.lastName) LIKE LOWER(:addedBy) OR ' +
            "CONCAT(LOWER(creatorUser.firstName), ' ', LOWER(creatorUser.lastName)) LIKE LOWER(:addedBy))",
          { addedBy: `%${addedBy}%` },
        );
      }

      // Apply date range filter
      if (
        createdDateRange &&
        Array.isArray(createdDateRange) &&
        createdDateRange.length === 2
      ) {
        try {
          const startDate = new Date(createdDateRange[0]);
          const endDate = new Date(createdDateRange[1]);

          // Ensure end date includes the entire day
          endDate.setHours(23, 59, 59, 999);

          // Log the dates for debugging

          queryBuilder.andWhere(
            'property.createdAt BETWEEN :startDate AND :endDate',
            { startDate, endDate },
          );
        } catch (error) {
          this.logger.error(
            `Error applying date range filter: ${error.message}`,
            error.stack,
          );
        }
      }

      // Apply owner filter
      if (owner) {
        // Join with property owner table
        queryBuilder
          .leftJoin('property_owner', 'po', 'po.property_id = property.id')
          .leftJoin('user', 'owner_user', 'owner_user.id = po.owner_id')
          .andWhere(
            '(LOWER(owner_user.firstName) LIKE LOWER(:owner) OR LOWER(owner_user.lastName) LIKE LOWER(:owner) OR LOWER(owner_user.email) LIKE LOWER(:owner))',
            { owner: `%${owner}%` },
          );
      }

      // Apply IDs filter
      if (ids && ids.length > 0) {
        queryBuilder.andWhere('property.id IN (:...ids)', { ids });
      }

      // Apply sorting - ensure default sort by creation date
      if (sortColumn && sortOrder) {
        // Handle property code (ID) sorting
        if (sortColumn === 'code') {
          queryBuilder.orderBy('property.code', sortOrder);
        }
        // Handle home type sorting
        else if (sortColumn === 'unit_type_name') {
          queryBuilder.orderBy('unit_type.name', sortOrder);
        }
        // For owner sorting, we'll handle it after fetching the data
        // since the owner data is fetched separately
        else if (sortColumn === 'city') {
          queryBuilder.orderBy('address.city', sortOrder, 'NULLS LAST');
        }
        // Default to creation date sorting
        else {
          queryBuilder.orderBy('property.createdAt', sortOrder);
        }
      } else {
        // Default sorting by creation date
        queryBuilder.orderBy('property.createdAt', 'DESC');
      }

      // Get total count before pagination
      const totalRecords = await queryBuilder.getCount();
      const totalRecordsWithoutFiltersQueryBuilder =
        await this.propertyRepository
          .createQueryBuilder('property')

          .leftJoinAndSelect('property.userRole', 'userRole')
          .leftJoinAndSelect('property.marketCenter', 'marketCenter')
          .leftJoinAndSelect('property.account', 'account');

      // Apply role-based filters for totalRecordsWithoutFilters
      if (currentRole?.name !== ROLES_ENUM.PLATFORM_ADMIN) {
        totalRecordsWithoutFiltersQueryBuilder.andWhere(
          new Brackets((qb) => {
            if (accountId) {
              qb.where('property.account_id = :accountId', { accountId });
            }
            if (marketCenterId) {
              qb.orWhere('property.market_center_id = :marketCenterId', {
                marketCenterId,
              });
            }

            if (agentUserRoleIds.length > 0) {
              qb.orWhere('userRole.id IN (:...agentUserRoleIds)', {
                agentUserRoleIds,
              });
            }

            if (currentUserRoleId) {
              qb.orWhere('userRole.id = :currentUserRoleId', {
                currentUserRoleId,
              });
            }
          }),
        );
      } else {
        this.logger.log(
          'Skipping all filters for property table for platform admin',
        );
      }

      const totalRecordsWithoutFilters =
        await totalRecordsWithoutFiltersQueryBuilder.getCount();

      // Apply pagination
      const skip = (page - 1) * limit;
      queryBuilder.skip(skip).take(limit);

      // Execute query
      const properties = await queryBuilder.getMany();

      // Get property IDs to fetch owners
      const propertyIds = properties.map((property) => property.id);

      // Fetch property owners separately
      const propertyOwners = await this.propertyOwnerRepository.find({
        where: {
          propertyId: In(propertyIds),
          isActive: true,
        },
        relations: ['owner', 'address'], // Add address relation
      });

      // Create a map of property ID to owner
      const propertyToOwnerMap = new Map();
      propertyOwners.forEach((po) => {
        propertyToOwnerMap.set(po.propertyId.toString(), po.owner);
      });

      // Map the data to include owner details and creator details with role
      const mappedProperties = properties.map((property) => {
        const propertyOwner = propertyOwners.find(
          (po) => po.propertyId.toString() === property.id.toString(),
        );
        const owner = propertyOwner?.owner;
        const ownerAddress = propertyOwner?.address;

        return {
          ...property,
          addedBy:
            property.userRole && property.userRole.user
              ? {
                  id: property.userRole.user.id,
                  firstName: property.userRole.user.firstName,
                  lastName: property.userRole.user.lastName,
                  role: property.userRole.role
                    ? property.userRole.role.name
                    : null,
                }
              : null,
          owner: owner
            ? {
                id: owner.id,
                firstName: owner.firstName,
                lastName: owner.lastName,
                email: owner.email,
                username: owner.username,
                contactNumber: owner.contactNumber,
              }
            : null,
          ownerAddress: ownerAddress
            ? {
                street: (ownerAddress as any).street,
                city: (ownerAddress as any).city,
                state: (ownerAddress as any).state,
                zipcode: (ownerAddress as any).zipcode,
              }
            : null,
        };
      });

      // Apply owner sorting after mapping if needed
      if (sortColumn === 'owner' && sortOrder) {
        mappedProperties.sort((a, b) => {
          const ownerNameA = a.owner
            ? `${a.owner.firstName} ${a.owner.lastName}`.toLowerCase()
            : '';
          const ownerNameB = b.owner
            ? `${b.owner.firstName} ${b.owner.lastName}`.toLowerCase()
            : '';

          if (sortOrder === 'ASC') {
            return ownerNameA.localeCompare(ownerNameB);
          } else {
            return ownerNameB.localeCompare(ownerNameA);
          }
        });
      }

      // Calculate pagination metadata
      const pageCount = Math.ceil(totalRecords / limit);
      const currentPage = Number(page);

      return {
        data: mappedProperties,
        meta: {
          limit: Number(limit),
          itemCount: properties.length,
          totalRecords,
          totalRecordsWithoutFilters,
          pageCount,
          hasPreviousPage: currentPage > 1,
          hasNextPage: currentPage < pageCount,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error fetching properties: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async exportPropertiesToExcel(
    ids: string[],
    sortSearchDataDto: SortSearchDataDto,
    filters: {
      status?: string;
      homeType?: string | string[];
      city?: string | string[];
      state?: string | string[];
      owner?: string;
      zipcode?: string;
      createdDateRange?: string[];
      propertyDetails?: string;
      addedBy?: string;
    },
    response: Response,
    currentUser?: string,
    currentRole?: any,
    account?: any,
    marketCenterId?: any,
    accountId?: any,
  ) {
    try {
      // Use the same query logic as getAllProperties but without pagination
      const queryBuilder = this.propertyRepository
        .createQueryBuilder('property')
        .leftJoinAndSelect('property.address', 'address')
        .leftJoinAndSelect('property.unit_type', 'unit_type')
        .leftJoinAndSelect('property.status', 'status')
        .leftJoinAndSelect('property.userRole', 'userRole')
        .leftJoinAndSelect('userRole.user', 'creatorUser')
        .leftJoinAndSelect('userRole.role', 'creatorRole')
        .leftJoinAndSelect('property.marketCenter', 'marketCenter')
        .leftJoinAndSelect('property.account', 'account');

      // 👇 Collect matching agent userRoleIds
      let agentUserRoleIds: any[] = [];

      // Step 1: Get agents by accountId or marketCenterId
      const agentWhere: any[] = [];
      if (accountId) {
        agentWhere.push({ account: { id: accountId }, isDeleted: false });
      }
      if (marketCenterId) {
        agentWhere.push({
          marketCenter: { id: marketCenterId },
          isDeleted: false,
        });
      }

      if (agentWhere.length > 0) {
        const agents = await this.agentRepository.find({
          where: agentWhere.length > 1 ? agentWhere : agentWhere[0],
          relations: ['user'],
        });

        const agentUserIds = agents.map((agent) => agent.user.id);

        // Step 2: Get agent userRoles
        const agentRoles = await this.roleRepository.find({
          where: { name: ROLES_ENUM.AGENT },
        });

        const agentRoleIds = agentRoles.map((role) => role.id);

        const agentUserRoles = await this.userRoleRepository.find({
          where: {
            userId: In(agentUserIds),
            roleId: In(agentRoleIds),
          },
          select: ['id'],
        });

        agentUserRoleIds = agentUserRoles.map((ur) => ur.id);
      }

      // Step 3: Get current user's role ID (MCM or Admin)
      let currentUserRoleId: string | null = null;
      const currentUserRole = await this.userRoleRepository.findOne({
        where: {
          userId: currentUser,
          roleId: currentRole.id,
          isDeleted: false,
        },
        select: ['id'],
      });

      if (currentUserRole?.id) {
        currentUserRoleId = currentUserRole.id;
      }

      // ✅ Step 4: Combine all filters using OR
      if (currentRole?.name !== ROLES_ENUM.PLATFORM_ADMIN) {
        queryBuilder.andWhere(
          new Brackets((qb) => {
            if (accountId) {
              qb.where('property.account_id = :accountId', { accountId });
            }
            if (marketCenterId) {
              qb.orWhere('property.market_center_id = :marketCenterId', {
                marketCenterId,
              });
            }

            if (agentUserRoleIds.length > 0) {
              qb.orWhere('userRole.id IN (:...agentUserRoleIds)', {
                agentUserRoleIds,
              });
            }

            if (currentUserRoleId) {
              qb.orWhere('userRole.id = :currentUserRoleId', {
                currentUserRoleId,
              });
            }
          }),
        );
      } else {
        this.logger.log(
          'Skipping all filters for property table for platform admin',
        );
      }

      // Apply filters
      const {
        status,
        homeType,
        city,
        state,
        owner,
        zipcode,
        createdDateRange,
        propertyDetails,
        addedBy,
      } = filters || {};

      // Apply status filter
      if (status) {
        queryBuilder.andWhere('status.name = :status', {
          status: status.toUpperCase(),
        });
      }

      // Apply home type (unit_type) filter
      if (homeType) {
        if (Array.isArray(homeType)) {
          queryBuilder.andWhere('unit_type.id IN (:...homeType)', { homeType });
        } else {
          queryBuilder.andWhere('unit_type.id = :homeType', { homeType });
        }
      }

      // Apply city filter
      if (city && (Array.isArray(city) ? city.length > 0 : city)) {
        if (Array.isArray(city)) {
          queryBuilder.andWhere('address.city IN (:...city)', { city });
        } else {
          queryBuilder.andWhere('address.city = :city', { city });
        }
      }

      // Apply state filter
      if (state && (Array.isArray(state) ? state.length > 0 : state)) {
        if (Array.isArray(state)) {
          queryBuilder.andWhere('address.state IN (:...state)', { state });
        } else {
          queryBuilder.andWhere('address.state = :state', { state });
        }
      }

      // Apply zipcode filter
      if (zipcode) {
        queryBuilder.andWhere('address.zipcode = :zipcode', { zipcode });
      }

      // Apply property details filter
      if (propertyDetails) {
        queryBuilder.andWhere(
          '(LOWER(property.unit_name) LIKE LOWER(:propertyDetails) OR ' +
            'LOWER(property.building_name) LIKE LOWER(:propertyDetails) OR ' +
            'LOWER(property.description) LIKE LOWER(:propertyDetails) OR ' +
            'LOWER(property.tax_id) LIKE LOWER(:propertyDetails))',
          { propertyDetails: `%${propertyDetails}%` },
        );
      }

      // Apply added by (creator) filter
      if (addedBy) {
        queryBuilder.andWhere(
          '(LOWER(creatorUser.firstName) LIKE LOWER(:addedBy) OR ' +
            'LOWER(creatorUser.lastName) LIKE LOWER(:addedBy) OR ' +
            "CONCAT(LOWER(creatorUser.firstName), ' ', LOWER(creatorUser.lastName)) LIKE LOWER(:addedBy))",
          { addedBy: `%${addedBy}%` },
        );
      }

      // Apply date range filter
      if (
        createdDateRange &&
        Array.isArray(createdDateRange) &&
        createdDateRange.length === 2
      ) {
        try {
          const startDate = new Date(createdDateRange[0]);
          const endDate = new Date(createdDateRange[1]);

          // Ensure end date includes the entire day
          endDate.setHours(23, 59, 59, 999);

          queryBuilder.andWhere(
            'property.createdAt BETWEEN :startDate AND :endDate',
            { startDate, endDate },
          );
        } catch (error) {
          this.logger.error(
            `Error applying date range filter: ${error.message}`,
            error.stack,
          );
        }
      }

      // Apply owner filter
      if (owner) {
        queryBuilder
          .leftJoin('property_owner', 'po', 'po.property_id = property.id')
          .leftJoin('user', 'owner_user', 'owner_user.id = po.owner_id')
          .andWhere(
            '(LOWER(owner_user.firstName) LIKE LOWER(:owner) OR ' +
              'LOWER(owner_user.lastName) LIKE LOWER(:owner) OR ' +
              'LOWER(owner_user.email) LIKE LOWER(:owner) OR ' +
              "CONCAT(LOWER(owner_user.firstName), ' ', LOWER(owner_user.lastName)) LIKE LOWER(:owner))",
            { owner: `%${owner}%` },
          );
      }

      // Apply IDs filter
      if (ids && ids.length > 0) {
        queryBuilder.andWhere('property.id IN (:...ids)', { ids });
      }

      // Default sorting by creation date
      queryBuilder.orderBy('property.createdAt', 'DESC');

      // Get all properties without pagination
      const properties = await queryBuilder.getMany();

      // Fetch property owners
      const propertyIds = properties.map((property) => property.id);
      const propertyOwners = await this.propertyOwnerRepository.find({
        where: {
          propertyId: In(propertyIds),
          isActive: true,
        },
        relations: ['owner'],
      });

      // Create a map of property ID to owner
      const propertyToOwnerMap = new Map();
      propertyOwners.forEach((po) => {
        propertyToOwnerMap.set(po.propertyId.toString(), po.owner);
      });

      // Helper function to format role names
      const formatRoleName = (role: string): string => {
        const roleMap: Record<string, string> = {
          market_center_manager: 'MC Manager',
          agent: 'Agent',
          owner: 'Owner',
          platform_admin: 'Platform Admin',
          company_admin: 'Company Admin',
          service_provider: 'Service Provider',
        };

        return roleMap[role.toLowerCase()] || role;
      };

      // Create Excel workbook
      const workbook = new Excel.Workbook();
      const worksheet = workbook.addWorksheet('Properties');

      // Define columns based on the requested headers
      worksheet.columns = [
        { header: 'Property ID', key: 'propertyId', width: 15 },
        { header: 'Home Type', key: 'homeType', width: 15 },
        { header: 'City', key: 'city', width: 15 },
        { header: 'Owner', key: 'owner', width: 20 },
        { header: 'Added By', key: 'addedBy', width: 25 },
        { header: 'Added On', key: 'addedOn', width: 15 },
        { header: 'Status', key: 'status', width: 15 },
      ];

      // Add rows
      properties.forEach((property) => {
        const owner = propertyToOwnerMap.get(property.id.toString());
        const ownerName = owner
          ? `${owner.firstName} ${owner.lastName}`
          : 'No Owner';
        const creatorName = property.userRole?.user
          ? `${property.userRole.user.firstName || ''} ${
              property.userRole.user.lastName || ''
            }`.trim()
          : '';
        const roleName = property.userRole?.role
          ? formatRoleName(property.userRole.role.name)
          : '';

        // Format the Added By column to include both name and role
        const addedByWithRole = creatorName
          ? roleName
            ? `${creatorName}\n(${roleName})`
            : creatorName
          : '';

        worksheet.addRow({
          propertyId: property.code || property.id,
          homeType: property.unit_type?.name || '',
          city: property.address?.city || '',
          owner: ownerName,
          addedBy: addedByWithRole,
          addedOn: property.createdAt
            ? new Date(property.createdAt).toLocaleDateString()
            : '',
          status: property.isActive ? 'Active' : 'Inactive',
        });
      });

      // Style the header row
      const headerRow = worksheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.eachCell((cell) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFE0E0E0' },
        };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      });

      // Set response headers
      response.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      response.setHeader(
        'Content-Disposition',
        'attachment; filename=properties.xlsx',
      );

      // Write to response and end
      await workbook.xlsx.write(response);
      response.end();

      return { success: true };
    } catch (error) {
      this.logger.error(
        `Error exporting properties to Excel: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to export properties: ${error.message}`);
    }
  }

  async getUserNameById(userId: string): Promise<string> {
    try {
      const userRole = await this.userRoleRepository
        .createQueryBuilder('userRole')
        .leftJoinAndSelect('userRole.user', 'user')
        .where('userRole.userId = :userId', { userId })
        .andWhere('userRole.isActive = :isActive', { isActive: true })
        .getOne();

      if (userRole && userRole.user) {
        return `${userRole.user.firstName || ''} ${
          userRole.user.lastName || ''
        }`.trim();
      }
      return '';
    } catch (error) {
      this.logger.error(`Error fetching user name: ${error.message}`);
      return '';
    }
  }

  async changePropertyStatus(id: string, isActive: boolean) {
    const updatedProperty = await this.dataSource.transaction(
      async (transactionalEntityManager) => {
        // First check if the property exists
        const property = await transactionalEntityManager.findOne(Property, {
          where: { id },
          relations: ['unit_type', 'address', 'status'],
        });

        if (!property) {
          throw new NotFoundException(`Property with ID ${id} not found`);
        }

        // Update the status
        await transactionalEntityManager
          .createQueryBuilder()
          .update(Property)
          .set({ isActive })
          .where('id = :id', { id })
          .execute();

        // Return the updated property
        return await transactionalEntityManager.findOne(Property, {
          where: { id },
          relations: ['unit_type', 'address', 'status'],
        });
      },
    );

    return {
      data: updatedProperty,
      message: `Property ${
        isActive ? 'activated' : 'deactivated'
      } successfully`,
    };
  }

  async checkTaxIdExists(taxId: string): Promise<boolean> {
    const count = await this.propertyRepository.count({
      where: { tax_id: taxId },
    });
    return count > 0;
  }

  async checkUserOwnsProperty(email: string): Promise<boolean> {
    // Skip check if email is empty
    if (!email || email.trim() === '') {
      return false;
    }

    // Find user by email
    const user = await this.userRepository.findOne({
      where: { email: email.toLowerCase(), isDeleted: false },
    });

    // If user doesn't exist, they don't own a property
    if (!user) {
      return false;
    }

    // Check if user owns a property
    const existingOwnership = await this.propertyOwnerRepository.findOne({
      where: { ownerId: user.id, isActive: true },
    });

    return !!existingOwnership;
  }

  findPropertyOwnerByQuery(ownerId: string, propertyId: string) {
    return this.propertyOwnerRepository.findOne({
      where: {
        ownerId,
        propertyId,
      },
    });
  }
  async getPropertyById(id: string) {
    // Find the property with related entities
    const property = await this.propertyRepository
      .createQueryBuilder('property')
      .leftJoinAndSelect('property.address', 'address')
      .leftJoinAndSelect('address.zipCode', 'zipCode') // 👈 This is needed
      .leftJoinAndSelect('property.status', 'status')
      .where('property.id = :id', { id })
      .getOne();

    const zipCodeId = property?.address?.zipCode?.id;
    if (!property) {
      throw new NotFoundException(`Property with ID ${id} not found`);
    }

    // Find the property owner relationship
    const propertyOwner = await this.propertyOwnerRepository.findOne({
      where: { propertyId: id, isActive: true },
      relations: ['owner', 'address', 'address.zipCode'],
    });

    // Combine the data
    return {
      ...property,
      zipCodeId: Number(zipCodeId) ?? null,
      owner: propertyOwner?.owner
        ? {
            id: propertyOwner.owner.id,
            firstName: propertyOwner.owner.firstName,
            lastName: propertyOwner.owner.lastName,
            email: propertyOwner.owner.email,
            username: propertyOwner.owner.username,
            phoneNumber: propertyOwner.owner.contactNumber,
          }
        : null,
      ownerAddress: propertyOwner?.address
        ? {
            ...propertyOwner.address,
            zipCodeId: Number(propertyOwner.address.zipCode?.id) ?? null,
            zipCode: undefined, // remove the nested object if you only want id
          }
        : null,
    };
  }

  async updateProperty(id: string, updatePropertyDto: UpdatePropertyDto) {
    // Check if property exists
    const existingProperty = await this.propertyRepository.findOne({
      where: { id },
      relations: ['address', 'unit_type'],
    });

    const unitType = await this.unitTypeRepository.findOne({
      where: [
        isNaN(+updatePropertyDto.propertyDetails.unit_type)
          ? { name: updatePropertyDto.propertyDetails.unit_type }
          : { id: updatePropertyDto.propertyDetails.unit_type },
      ],
    });
    if (!existingProperty) {
      throw new NotFoundException(`Property with ID ${id} not found`);
    }

    // Check if tax ID is unique if provided and changed
    if (
      updatePropertyDto.propertyDetails.tax_id &&
      updatePropertyDto.propertyDetails.tax_id !== existingProperty.tax_id
    ) {
      const taxIdExists = await this.checkTaxIdExists(
        updatePropertyDto.propertyDetails.tax_id,
      );

      if (taxIdExists) {
        throw new BadRequestException('Tax ID already exists');
      }
    }

    // Find property owner
    const propertyOwner = await this.propertyOwnerRepository.findOne({
      where: { propertyId: id, isActive: true },
      relations: ['owner', 'address', 'address.zipCode'],
    });

    return this.dataSource.transaction(async (transactionalEntityManager) => {
      // 1. Update property details

      // Update property entity with extracted values
      await transactionalEntityManager.update(Property, id, {
        tax_id:
          updatePropertyDto.propertyDetails.tax_id || existingProperty.tax_id,
        unit_type_id: unitType ? unitType.id : existingProperty.unit_type_id,
        unit_type: unitType || existingProperty.unit_type,
        unit_name:
          updatePropertyDto.propertyDetails.unit_name ||
          existingProperty.unit_name,
        building_name:
          updatePropertyDto.propertyDetails.building_name ||
          existingProperty.building_name,
        floor_count: updatePropertyDto.propertyDetails.floor_count
          ? Number(updatePropertyDto.propertyDetails.floor_count)
          : existingProperty.floor_count,
        rooms_count: updatePropertyDto.propertyDetails.rooms_count
          ? Number(updatePropertyDto.propertyDetails.rooms_count)
          : existingProperty.rooms_count,
        bed_count: updatePropertyDto.propertyDetails.bed_count
          ? Number(updatePropertyDto.propertyDetails.bed_count)
          : existingProperty.bed_count,
        full_bath_count: updatePropertyDto.propertyDetails.full_bath_count
          ? Number(updatePropertyDto.propertyDetails.full_bath_count)
          : existingProperty.full_bath_count,
        half_bath_count: updatePropertyDto.propertyDetails.half_bath_count
          ? Number(updatePropertyDto.propertyDetails.half_bath_count)
          : existingProperty.half_bath_count,
        three_fourth_bath_count: updatePropertyDto.propertyDetails
          .three_fourth_bath_count
          ? Number(updatePropertyDto.propertyDetails.three_fourth_bath_count)
          : existingProperty.three_fourth_bath_count,
        one_fourth_bath_count: updatePropertyDto.propertyDetails
          .one_fourth_bath_count
          ? Number(updatePropertyDto.propertyDetails.one_fourth_bath_count)
          : existingProperty.one_fourth_bath_count,
        basement_size: updatePropertyDto.propertyDetails.basement_size
          ? Number(updatePropertyDto.propertyDetails.basement_size)
          : existingProperty.basement_size,
        garage_size: updatePropertyDto.propertyDetails.garage_size
          ? Number(updatePropertyDto.propertyDetails.garage_size)
          : existingProperty.garage_size,
        finished_size: updatePropertyDto.propertyDetails.finished_size
          ? Number(updatePropertyDto.propertyDetails.finished_size)
          : existingProperty.finished_size,
        lot_size: updatePropertyDto.propertyDetails.lot_size
          ? Number(updatePropertyDto.propertyDetails.lot_size)
          : existingProperty.lot_size,
        year_of_built:
          updatePropertyDto.propertyDetails.year_of_built ||
          existingProperty.year_of_built,
        description:
          updatePropertyDto.propertyDetails.description ||
          existingProperty.description,
        updatedBy: updatePropertyDto.updatedBy || existingProperty.updatedBy,
        updatedAt: new Date(),
      });

      // 2. Update property address
      if (updatePropertyDto.propertyAddress && existingProperty.address) {
        await transactionalEntityManager.update(
          Address,
          existingProperty.address.id,
          {
            street:
              updatePropertyDto.propertyAddress.street ||
              existingProperty.address.street,
            city:
              updatePropertyDto.propertyAddress.city ||
              existingProperty.address.city,
            state:
              updatePropertyDto.propertyAddress.state ||
              existingProperty.address.state,
            zipcode:
              updatePropertyDto.propertyAddress.zipcode ||
              existingProperty.address.zipcode,
            updatedAt: new Date(),
          },
        );
      }

      // 3. Update property owner if exists
      if (propertyOwner && updatePropertyDto.owner) {
        // Update owner details
        if (propertyOwner.owner) {
          await transactionalEntityManager.update(
            User,
            propertyOwner.owner.id,
            {
              firstName:
                updatePropertyDto.owner.firstName ||
                propertyOwner.owner.firstName,
              lastName:
                updatePropertyDto.owner.lastName ||
                propertyOwner.owner.lastName,
              contactNumber:
                updatePropertyDto.owner.phoneNumber ||
                propertyOwner.owner.contactNumber,
              email: updatePropertyDto.owner.email || propertyOwner.owner.email,
              updatedAt: new Date(),
            },
          );
        }

        // Update owner address if exists
        if (
          propertyOwner.address &&
          updatePropertyDto.owner.address &&
          Number(updatePropertyDto.owner.address?.zipId) ===
            Number(propertyOwner.address?.zipCode?.id)
        ) {
          await transactionalEntityManager.update(
            Address,
            propertyOwner.address.id,
            {
              street:
                updatePropertyDto.owner.address.street ||
                propertyOwner.address.street,
              city:
                updatePropertyDto.owner.address.city ||
                propertyOwner.address.city,
              state:
                updatePropertyDto.owner.address.state ||
                propertyOwner.address.state,
              zipcode:
                updatePropertyDto.owner.address.zipcode ||
                propertyOwner.address.zipcode,
              updatedAt: new Date(),
            },
          );
        } else {
          const newAddrerss = await transactionalEntityManager.create(Address, {
            street: updatePropertyDto.owner.address.street,
            city: updatePropertyDto.owner.address.city,
            state: updatePropertyDto.owner.address.state,
            zipcode: updatePropertyDto.owner.address.zipcode,
            zipCode: updatePropertyDto.owner.address.zipId,
          });
          const saveAddress = await transactionalEntityManager.save(
            Address,
            newAddrerss,
          );
          await transactionalEntityManager.update(
            PropertyOwner,
            propertyOwner.id,
            {
              addressId: saveAddress.id,
              address: saveAddress,
            },
          );
        }
      }
      // If there is no propertyOwner but DTO contains owner info
      if (!propertyOwner && updatePropertyDto.owner?.email) {
        // Check if user exists
        let user = await this.userRepository.findOne({
          where: {
            username: updatePropertyDto.owner.email.toLowerCase(),
            isDeleted: false,
          },
        });

        const isNewUser = !user;
        let generatedPassword: string | null = null;
        let existingOwnership: PropertyOwner | null = null;

        if (user) {
          // Update basic info if user already exists
          user.firstName = updatePropertyDto.owner.firstName || user.firstName;
          user.lastName = updatePropertyDto.owner.lastName || user.lastName;
          user.contactNumber =
            updatePropertyDto.owner.phoneNumber || user.contactNumber;
          await transactionalEntityManager.save(user);

          existingOwnership = await this.propertyOwnerRepository.findOne({
            where: { ownerId: user.id, isActive: true },
          });
        } else {
          // Create new user
          user = this.userRepository.create({
            email: updatePropertyDto.owner.email.toLowerCase(),
            username: updatePropertyDto.owner.email.toLowerCase(),
            firstName: updatePropertyDto.owner.firstName || '',
            lastName: updatePropertyDto.owner.lastName || '',
            contactNumber: updatePropertyDto.owner.phoneNumber || '',
            isActive: true,
            isDeleted: false,
          });

          user = await transactionalEntityManager.save(user);
        }

        // Assign 'owner' role if not assigned already
        const ownerRole = await this.roleRepository.findOne({
          where: { name: ROLES_ENUM.OWNER },
        });

        if (ownerRole) {
          const existingUserRole = await this.userRoleRepository.findOne({
            where: {
              userId: user.id,
              roleId: ownerRole.id,
              isDeleted: false,
            },
          });

          if (!existingUserRole) {
            const newUserRole = this.userRoleRepository.create({
              userId: user.id,
              roleId: ownerRole.id,
              isActive: true,
              isDeleted: false,
            });

            await transactionalEntityManager.save(newUserRole);
          }
        }

        // Create new owner address
        let ownerAddress = null;
        if (updatePropertyDto.owner.address) {
          ownerAddress = this.addressRepository.create({
            street: updatePropertyDto.owner.address.street,
            city: updatePropertyDto.owner.address.city,
            state: updatePropertyDto.owner.address.state,
            zipcode: updatePropertyDto.owner.address.zipcode,
            zipCode: updatePropertyDto.owner.address.zipId,
          });

          ownerAddress = await transactionalEntityManager.save(ownerAddress);
        }

        // Create property owner entry
        const newPropertyOwner = this.propertyOwnerRepository.create({
          ownerId: user.id,
          propertyId: existingProperty.id,
          isActive: true,
          ownershipStartDate: new Date(),
          addressId: ownerAddress?.id,
        });

        await transactionalEntityManager.save(newPropertyOwner);
        let marketCenterName = 'HomeWorks';
        let creatorName = 'HomeWorks Admin';
        let creator;
        if (updatePropertyDto.updatedBy && updatePropertyDto.roleId) {
          creator = await this.userRepository.findOne({
            where: { id: String(updatePropertyDto.updatedBy) },
          });

          if (creator) {
            creatorName = `${creator.firstName || ''} ${
              creator.lastName || ''
            }`.trim();

            const role = await this.roleRepository.findOne({
              where: { id: updatePropertyDto.roleId },
            });

            if (role?.name === ROLES_ENUM.MARKET_CENTER_MANAGER) {
              const userRole = await this.userRoleRepository.findOne({
                where: {
                  roleId: updatePropertyDto.roleId,
                  userId: updatePropertyDto.updatedBy,
                },
              });
              const marketCenterUser =
                await this.marketCenterUserRepository.findOne({
                  where: {
                    userRole: { id: userRole.id },
                  },
                  relations: ['marketCenter'],
                });

              if (marketCenterUser?.marketCenter?.name) {
                marketCenterName = marketCenterUser.marketCenter?.name;
              }
            } else if (role?.name === ROLES_ENUM.COMPANY_ADMIN) {
              const userRole = await this.userRoleRepository.findOne({
                where: {
                  roleId: updatePropertyDto.roleId,
                  userId: updatePropertyDto.updatedBy,
                },
              });
              const companyAdmin = await this.accountUserRepository.findOne({
                where: { userRoleId: userRole.id },
                relations: ['account'],
              });

              if (companyAdmin?.account?.name) {
                marketCenterName = companyAdmin.account.name;
              }
            }
          }
        }

        if (isNewUser || !existingOwnership) {
          const { password, hashedPassword } =
            await this.commonService.generateSecurePassword();
          generatedPassword = password;

          await transactionalEntityManager.update(User, user.id, {
            password: hashedPassword,
          });
          const propertyAddressStr = `${existingProperty?.address.street}, ${existingProperty?.address.city}, ${existingProperty?.address.state} ${existingProperty?.address.zipcode}`;

          await this.sendNewOwnerWelcomeEmail(
            user.email,
            user.firstName,
            user.lastName,
            user.username,
            user.contactNumber,
            creatorName,
            marketCenterName,
            generatedPassword,
            propertyAddressStr,
            creator.email,
            creator.contactNumber,
          );
        } else {
          await this.sendPropertyAddedEmailForExistingUser(
            user.email,
            user.firstName,
            user.lastName,
            user.username,
            user.contactNumber,
            creatorName,
            marketCenterName,
          );
        }

        const propertyStatus =
          await this.propertyApprovalStatusRepository.findOne({
            where: {
              name: 'PENDING FROM OWNER',
            },
          });
        await transactionalEntityManager.update(Property, id, {
          status: propertyStatus,
          statusId: propertyStatus.id,
        });
      }

      // Fetch and return the updated property
      const updatedProperty = await transactionalEntityManager.findOne(
        Property,
        {
          where: { id },
          relations: ['address', 'unit_type', 'status'],
        },
      );

      return updatedProperty;
    });
  }

  async softDeleteOwner(ownerId: string) {
    const data = await this.propertyRepositoryy.softDeleteOwner(ownerId);
    return {
      data: data,
      message: `user deleted sucessfully`,
    };
  }

  async getPropertiesByLoggedInUser(userId: string) {
    try {
      // Step 1: Fetch all properties created by the logged-in user
      const properties = await this.propertyRepository
        .createQueryBuilder('property')
        .leftJoinAndSelect('property.address', 'address')
        .leftJoinAndSelect('address.zipCode', 'zipCode')
        .leftJoinAndSelect('property.status', 'status')
        .leftJoinAndSelect('property.unit_type', 'unitType')
        .where('property.createdBy = :userId', { userId })
        .andWhere('property.isDeleted = false')
        .orderBy('property.createdAt', 'DESC')
        .getMany();

      if (!properties.length) {
        throw new NotFoundException(
          `No properties found for user ID ${userId}`,
        );
      }

      // Step 2: For each property, get the property owner + address + zipCode
      const propertiesWithOwner = await Promise.all(
        properties.map(async (property) => {
          const propertyOwner = await this.propertyOwnerRepository.findOne({
            where: {
              propertyId: property.id,
              isDeleted: false,
            },
            relations: ['owner', 'address', 'address.zipCode'],
          });

          // const zipCodeId = property?.address?.zipCode?.id;
          // const zipCodeId = property?.address?.zipCode?.id;
          const statusName = property.status?.name ?? null;

          const isStatusApproved =
            statusName === 'APPROVED' || statusName === 'APPROVED FROM OWNER';
          return {
            ...property,
            status: statusName,
            unit_type: property.unit_type?.name ?? null,
            isStatusApproved,
            owner: propertyOwner?.owner
              ? {
                  id: propertyOwner.owner.id,
                  firstName: propertyOwner.owner.firstName,
                  lastName: propertyOwner.owner.lastName,
                  email: propertyOwner.owner.email,
                  username: propertyOwner.owner.username,
                  phoneNumber: propertyOwner.owner.contactNumber,
                }
              : null,
            ownerAddress: propertyOwner?.address
              ? {
                  id: propertyOwner.address.id,
                  street: propertyOwner.address.street,
                  city: propertyOwner.address.city,
                  state: propertyOwner.address.state,
                  zipcode: propertyOwner.address.zipcode,
                  zipCode: Number(propertyOwner.address.zipCode?.id) ?? null,
                }
              : null,
          };
        }),
      );

      // Step 3: Return response
      return {
        properties: propertiesWithOwner,
        meta: {
          itemCount: propertiesWithOwner.length,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error fetching properties for user ${userId}: ${error.message}`,
        error,
      );
      throw error;
    }
  }

  async updatePropertyApprovalStatus(
    id: string,
    updatePropertyStatusDto: UpdatePropertyApprovalStatusDto,
    accountId?: string,
    marketCenterId?: string,
  ) {
    const currentUser = updatePropertyStatusDto.currentUser;
    const currentUserRole = updatePropertyStatusDto.currentUserRole;
    const property = await this.propertyRepository.findOne({
      where: { id, isDeleted: false },
      relations: ['status', 'address'],
    });

    if (!property) {
      throw new NotFoundException(`Property with id ${id} not found`);
    }

    // Check for "APPROVED FROM MCM" logic
    if (updatePropertyStatusDto.status === 'APPROVED FROM MCM') {
      // Find property owner
      const propertyOwner = await this.propertyOwnerRepository.findOne({
        where: { propertyId: id, isActive: true },
        relations: ['owner'],
      });
      let creatorName = 'HomeWorks Admin';
      let marketCenterOrCompanyName = 'HomeWorks';
      let senderEmail: string;
      let senderNumber: string;
      const propertyAddressStr = `${property?.address?.street}, ${property?.address?.city}, ${property?.address?.state} ${property?.address?.zipcode}`;

      if (currentUser && currentUserRole) {
        const creator = await this.userRepository.findOne({
          where: { id: String(currentUser) },
        });
        if (creator) {
          creatorName = `${creator.firstName || ''} ${
            creator.lastName || ''
          }`.trim();
          senderEmail = creator?.email;
          senderNumber = creator?.contactNumber;
        }

        const role = await this.roleRepository.findOne({
          where: { id: currentUserRole },
        });

        if (role?.name === ROLES_ENUM.MARKET_CENTER_MANAGER && marketCenterId) {
          const marketCenter = await this.marketCenterRepository.findOne({
            where: { id: marketCenterId },
          });
          if (marketCenter?.name) {
            marketCenterOrCompanyName = marketCenter.name;
          }
        } else if (role?.name === ROLES_ENUM.COMPANY_ADMIN && accountId) {
          const account = await this.accountUserRepository.findOne({
            where: { account: { id: accountId } },
            relations: ['account'],
          });
          if (account?.account?.name) {
            marketCenterOrCompanyName = account.account.name;
          }
        }
      }

      if (!propertyOwner || !propertyOwner.owner) {
        // No owner, set status to APPROVED
        const approvedStatus =
          await this.propertyApprovalStatusRepository.findOne({
            where: { name: 'APPROVED' },
          });
        property.status = approvedStatus;
        property.statusId = approvedStatus.id;
        property.comment = updatePropertyStatusDto.comment;
        await this.propertyRepository.save(property);
        return { message: 'Property approved (no owner assigned)' };
      } else {
        // Owner exists, check if they own any other property
        const ownerId = propertyOwner.owner.id;
        const otherOwnership = await this.propertyOwnerRepository.findOne({
          where: { ownerId, isActive: true, propertyId: Not(id) },
        });

        if (otherOwnership) {
          // Owner has another property, send approve/reject email, set status to PENDING FROM OWNER
          const pendingFromOwnerStatus =
            await this.propertyApprovalStatusRepository.findOne({
              where: { name: 'PENDING FROM OWNER' },
            });
          property.status = pendingFromOwnerStatus;
          property.statusId = pendingFromOwnerStatus.id;
          property.comment = updatePropertyStatusDto.comment;

          await this.propertyRepository.save(property);

          // Send approval/reject email (implement your email logic here)
          await this.sendPropertyAddedEmailForExistingUser(
            propertyOwner.owner.email,
            propertyOwner.owner.firstName,
            propertyOwner.owner.lastName,
            propertyOwner.owner.username,
            propertyOwner.owner.contactNumber,
            creatorName,
            marketCenterOrCompanyName,
          );

          return {
            message:
              'Approval/reject email sent to owner, status set to PENDING FROM OWNER',
          };
        } else {
          // Owner does not own any other property, send credentials email, set status to APPROVED
          const approvedStatus =
            await this.propertyApprovalStatusRepository.findOne({
              where: { name: 'APPROVED' },
            });
          property.status = approvedStatus;
          property.statusId = approvedStatus.id;
          property.comment = updatePropertyStatusDto.comment;

          await this.propertyRepository.save(property);
          const { password: generatedPassword, hashedPassword } =
            await this.commonService.generateSecurePassword();

          // 2. Update password in user repository
          await this.userRepository.update(propertyOwner.owner.id, {
            password: hashedPassword,
          });

          // Send credentials email (implement your email logic here)
          await this.sendNewOwnerWelcomeEmail(
            propertyOwner.owner.email,
            propertyOwner.owner.firstName,
            propertyOwner.owner.lastName,
            propertyOwner.owner.username,
            propertyOwner.owner.contactNumber,
            creatorName,
            marketCenterOrCompanyName,
            generatedPassword,
            propertyAddressStr,
            senderEmail,
            senderNumber,
            // Generate and pass password as needed
          );

          return {
            message:
              'Credentials email sent to new owner, status set to APPROVED',
          };
        }
      }
    }

    // Default: update status as requested
    const newStatus = await this.propertyApprovalStatusRepository.findOne({
      where: { name: updatePropertyStatusDto.status },
    });

    if (!newStatus) {
      throw new BadRequestException(
        `Invalid status: ${updatePropertyStatusDto.status}`,
      );
    }

    property.status = newStatus;
    property.statusId = newStatus.id;
    property.comment = updatePropertyStatusDto.comment;

    await this.propertyRepository.save(property);

    return { message: 'Property status updated successfully' };
  }
}
