import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
} from '@shadcn/ui';
import CardLayout from '../../../components/cardLayout';
import { ReactComponent as ValidationErrorIcon } from '../../../assets/icons/ValidationErrorIcon.svg';
import { Controller, useFormContext } from 'react-hook-form';
import { ReactComponent as LocationIcon } from '../../../assets/icons/LocationIcon.svg';
import { useQuery } from '@tanstack/react-query';
import { getAllZipCode } from '../api/actions';
import { useState, useEffect } from 'react';
import useDebounce from '../../../hooks/useDebounce';
import { ZipCityState } from './schema';
import { FileUpload } from '../../../components/file-upload';
import CommonDatePicker from '../../../components/commonDatePicker';
import { DateValueType } from 'react-tailwindcss-datepicker';
import { ReactComponent as BasicInfo } from '../../../assets/icons/BasicInfo.svg';
import { useParams } from 'react-router-dom';
import { formatPhoneForDisplay, formatPhoneForStorage } from '../../../components/phoneNumberFormate';

type FilterState = {
  dob: DateValueType;
};

const AddEditAgentDetails = () => {
  const { id } = useParams(); // Get the agent ID from URL if in edit mode
  const isEditMode = Boolean(id);
  const [isPrimaryZipOpen, setIsPrimaryZipOpen] = useState(false);
  const form = useFormContext();
  const { watch, setValue } = form;

  const zipCodeValue = watch('address.zipcode', '');
  const debouncedPrimaryZip = useDebounce(zipCodeValue, 500);
  const transformZipData = (data: ZipCityState[] | undefined) => {
    if (!data) return [];
    return data.map((item) => ({
      label: `${item.zipCode} - ${item.cityName}, ${item.stateName}`,
      value: item.zipCode,
      city: item.cityName,
      state: item.stateName,
    }));
  };

  const { data: primaryZipData, isLoading: isLoadingPrimaryZip } = useQuery({
    queryKey: ['zipCityState', 'primary', debouncedPrimaryZip],
    queryFn: () => getAllZipCode(debouncedPrimaryZip),
    enabled:
      !!debouncedPrimaryZip &&
      debouncedPrimaryZip.length >= 3 &&
      debouncedPrimaryZip.length <= 5 &&
      /^\d+$/.test(debouncedPrimaryZip),
    retry: false,
  });

  const primaryZipOptions = transformZipData(primaryZipData?.data);
  const handlePrimaryZipSelect = (option: any) => {
    setValue('address.zipcode', option.value, { shouldValidate: true });
    setValue('address.city', option.city, { shouldValidate: true });
    setValue('address.state', option.state, { shouldValidate: true });
    setValue('address.zipId', option.zipId, { shouldValidate: true });
    setValue('address.cityId', option.cityId);
    setValue('address.stateId', option.stateId);
    setIsPrimaryZipOpen(false);

    // Trigger validation immediately after setting values
    form.trigger([
      'address.zipcode',
      'address.zipId',
      'address.city',
      'address.state',
    ]);
  };

  const handleFilterChange = (field: keyof FilterState, value: any) => {
    if (field === 'dob') {
      // Convert to ISO string format for the form
      const dateValue = value?.startDate
        ? new Date(value.startDate).toISOString()
        : '';
      form.setValue('user.DOB', dateValue, { shouldValidate: true });
    }
  };

  const dateOfBirth = form.watch('user.DOB');
  const DOB = dateOfBirth
    ? {
        startDate: new Date(dateOfBirth),
        endDate: new Date(dateOfBirth),
      }
    : null;

  const profileImageUrl = form.watch('user.profileImageUrl');
  const [profileImageFile, setProfileImageFile] = useState<File | null>(null);
  const [profileImageKey, setProfileImageKey] = useState(0);

  useEffect(() => {
    // Only proceed if profileImageUrl has a valid url property
    if (profileImageUrl) {
      try {
        const parsedUrl = profileImageUrl.url;
        const fileType = profileImageUrl.type;
        const originalFilename = profileImageUrl.originalFilename;

        if (parsedUrl) {
          const fileName =
            originalFilename ||
            decodeURIComponent(parsedUrl.split('/').at(-1) || 'profile.jpg');
          fetch(parsedUrl)
            .then((response) => response.blob())
            .then((blob) => {
              const file = new File([blob], fileName, {
                type: fileType || blob.type,
              });
              setProfileImageFile(file);
            });
        }
      } catch (error) {
        setProfileImageFile(null);
      }
    } else {
      setProfileImageFile(null);
    }
  }, [profileImageUrl]);

  // When loading a new agent or clearing the form
  useEffect(() => {
    // Increment the key to force re-render of the FileUpload component
    setProfileImageKey((prev) => prev + 1);
  }, []);

  return (
    <div className="bg-white rounded-lg border p-1 flex flex-col">
      <CardLayout
        title="Personal Information"
        containerClassName="bg-none p-0 border-none shadow-none"
        componentClassName="p-3 text-[16px] px-[24px] m-0"
      />
      <div className="p-[20px] flex flex-col gap-[20px]">
        <div className="flex items-center gap-[12px]">
          <BasicInfo />
          <span className="text-hw-neutral-850 text-lg font-semibold">
            Basic Information
          </span>
        </div>
        <div className="flex flex-col gap-[20px]">
          {/* First Row */}
          <div className="flex gap-[20px]">
            <FormField
              control={form?.control}
              name="user.firstName"
              render={({ field }) => (
                <FormItem className="space-y-0 w-[33.5%]">
                  <FormLabel className="text-hw-neutral-700 text-xs font-normal dark:text-hw-neutral-300">
                    First Name<span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter first name"
                      {...field}
                      className="h-[44px]"
                    />
                  </FormControl>
                  <FormMessage
                    className="m-0 text-xs flex items-start pt-1"
                    errorIcon={
                      <ValidationErrorIcon className="h-3.5 w-3.5 text-red-500" />
                    }
                  />
                </FormItem>
              )}
            />

            <FormField
              control={form?.control}
              name="user.lastName"
              render={({ field }) => (
                <FormItem className="space-y-0 w-[33.5%]">
                  <FormLabel className="text-hw-neutral-700 text-xs font-normal dark:text-hw-neutral-300">
                    Last Name<span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter last name"
                      {...field}
                      className="h-[44px]"
                    />
                  </FormControl>
                  <FormMessage
                    className="m-0 text-xs flex items-start pt-1"
                    errorIcon={
                      <ValidationErrorIcon className="h-3.5 w-3.5 text-red-500" />
                    }
                  />
                </FormItem>
              )}
            />

            <div className="flex gap-[20px] w-[35%]">
              <FormField
                control={form.control}
                name="user.contactNumber"
                render={({ field }) => (
                  <FormItem className="space-y-0 w-1/2">
                    <FormLabel className="text-hw-neutral-700 text-xs font-normal dark:text-hw-neutral-300">
                      Phone Number<span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter number"
                        value={formatPhoneForDisplay(field.value || '')}
                      onChange={(e) => {
                        const formattedValue = formatPhoneForStorage(
                          e.target.value,
                        );
                        field.onChange(formattedValue);
                      }}
                        className="h-[44px]"
                      />
                    </FormControl>
                    <FormMessage
                      className="m-0 text-xs flex items-start pt-1"
                      errorIcon={
                        <ValidationErrorIcon className="h-3.5 w-3.5 text-red-500" />
                      }
                    />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="user.DOB"
                render={() => (
                  <FormItem className="space-y-0 w-1/2">
                    <FormLabel className="text-hw-neutral-700 text-xs font-normal dark:text-hw-neutral-300">
                      Date Of Birth<span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <CommonDatePicker
                        handleDateChange={(value) =>
                          handleFilterChange('dob', value)
                        }
                        placeholder="Select date"
                        inputClassName="w-full h-[44px] placeholder:hw-netural-400 rounded-lg placeholder:font-normal placeholder:text-sm"
                        containerClassName="relative w-full h-[44px]"
                        useRange={false}
                        asSingle={true}
                        initialValue={DOB}
                        showShortcuts={false}
                      />
                    </FormControl>
                    <FormMessage
                      className="m-0 text-xs flex items-start pt-1"
                      errorIcon={
                        <ValidationErrorIcon className="h-3.5 w-3.5 text-red-500" />
                      }
                    />
                  </FormItem>
                )}
              />
            </div>
          </div>

          <div className="flex flex-col md:flex-row gap-[20px]">
            <FormField
              control={form?.control}
              name="user.email"
              render={({ field }) => (
                <FormItem className="space-y-0 w-[50%]">
                  <FormLabel className="text-hw-neutral-700 text-xs font-normal dark:text-hw-neutral-300">
                    Email Address<span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter email address"
                      {...field}
                      className="h-[44px]"
                      onChange={(e) => {
                        field.onChange(e);

                        // Only update username if we're not in edit mode
                        if (!isEditMode) {
                          setValue('user.username', e.target.value);
                        }
                      }}
                    />
                  </FormControl>
                  <FormMessage
                    className="m-0 text-xs flex items-start pt-1"
                    errorIcon={
                      <ValidationErrorIcon className="h-3.5 w-3.5 text-red-500" />
                    }
                  />
                </FormItem>
              )}
            />

            <FormField
              control={form?.control}
              name="user.username"
              render={({ field }) => (
                <FormItem className="space-y-0 w-[50%]">
                  <FormLabel className="text-hw-neutral-700 text-xs font-normal">
                    Username<span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      value={field.value || ''}
                      placeholder="Username"
                      className="w-full h-11 bg-hw-neutral-50 disabled:text-[#6B7280]"
                      disabled
                    />
                  </FormControl>
                  <FormMessage className="m-0 text-xs flex pt-1" />
                </FormItem>
              )}
            />
          </div>

          <div className="flex flex-col gap-[0px] w-[32%] 2xl:w-[24%]">
            <FileUpload
              key={`profile-image-${profileImageKey}`}
              label="Add Profile Photo"
              required={false}
              name="user.profileImageUrl"
              maxSizeMB={5}
              register={form.register}
              setValue={setValue}
              resetField={form.resetField}
              setError={form.setError}
              error={
                (
                  form.formState.errors.user as any
                )?.profileImageUrl?.message?.toString() || ''
              }
              file={profileImageFile || undefined}
              clearErrors={form.clearErrors}
              previewType="thumbnail"
              accept="image/jpeg, image/png, image/jpg"
              previewUrl={
                profileImageFile
                  ? URL.createObjectURL(profileImageFile)
                  : profileImageUrl &&
                    typeof profileImageUrl === 'object' &&
                    'url' in profileImageUrl
                  ? profileImageUrl.url
                  : ''
              }
              showDeleteButton={true}
              setFile={(file) => {
                setProfileImageFile(file as File);
              }}
              showDownloadButton={true}
            />
            <FormMessage
              className="m-0 text-xs flex items-start pt-1"
              errorIcon={
                <ValidationErrorIcon className="h-3.5 w-3.5 text-red-500" />
              }
            />
          </div>

          {/* Second Row */}
        </div>
        <div className="flex flex-col gap-[20px]">
          <div className="flex items-center gap-[12px]">
            <LocationIcon />
            <span className="text-hw-neutral-850 text-lg font-semibold ">
              Address
            </span>
          </div>

          {/* Address Form */}
          <div className="flex  gap-[20px]">
            <FormField
              control={form.control}
              name="address.street"
              render={({ field }) => (
                <FormItem className="space-y-0 flex-1">
                  <FormLabel className="text-hw-neutral-700 text-xs font-normal dark:text-hw-neutral-300">
                    Street Address<span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter street address"
                      {...field}
                      className="h-[44px]"
                    />
                  </FormControl>
                  <FormMessage
                    className="m-0 text-xs flex items-center pt-1"
                    errorIcon={
                      <ValidationErrorIcon className="h-3.5 w-3.5 text-red-500" />
                    }
                  />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="address.zipcode"
              render={({ field }) => (
                <FormItem className="space-y-0 w-[170px]">
                  <FormLabel className="text-hw-neutral-700 text-xs font-normal dark:text-hw-neutral-300">
                    Zip Code<span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        placeholder="Enter zip code"
                        {...field}
                        className="h-[44px]"
                        value={field.value || ''}
                        onChange={(e) => {
                          const value = e.target.value;
                          field.onChange(value);

                          form.trigger(['address.zipcode']);

                          // Clear related fields without triggering validation
                          setValue('address.city', '', {
                            shouldValidate: false,
                          });
                          setValue('address.state', '', {
                            shouldValidate: false,
                          });
                          setValue('address.zipId', null, {
                            shouldValidate: false,
                          });
                          setValue('address.cityId', null, {
                            shouldValidate: false,
                          });
                          setValue('address.stateId', null, {
                            shouldValidate: false,
                          });

                          // Show/hide dropdown based on input
                          const isValidZip =
                            /^\d+$/.test(value) && value.length <= 5;
                          setIsPrimaryZipOpen(value.length >= 3 && isValidZip);
                        }}
                        onBlur={() => {
                          field.onBlur();
                          setTimeout(() => {
                            setIsPrimaryZipOpen(false);
                            // Only validate on blur
                            if (field.value && field.value.length === 5) {
                              form.trigger(['address.zipcode']);
                            }
                          }, 200);
                          // form.trigger(['address.zipcode']);
                          // }, 200);
                        }}
                      />
                      {isPrimaryZipOpen && (
                        <div className="absolute left-0 right-0 mt-1 bg-white border rounded-md shadow-lg z-10 max-h-[200px] overflow-y-auto">
                          {isLoadingPrimaryZip ? (
                            <div className="px-3 py-2 text-sm text-gray-500">
                              Loading...
                            </div>
                          ) : primaryZipOptions.length > 0 ? (
                            primaryZipOptions.map((option) => (
                              <button
                                type="button"
                                key={option.value}
                                className="px-3 py-2 w-full text-left hover:bg-hw-primary text-sm"
                                onMouseDown={() => {
                                  handlePrimaryZipSelect(option);
                                  // Validate after selection
                                  setTimeout(() => {
                                    form.trigger(['address.zipcode']);
                                  }, 100);
                                }}
                              >
                                {option.label}
                              </button>
                            ))
                          ) : (
                            <div className="px-3 py-2 text-sm text-gray-500">
                              No results found
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </FormControl>
                  <FormMessage
                    className="m-0 text-xs items-start pt-1"
                    errorIcon={
                      <ValidationErrorIcon className="min-h-3.5 min-w-3.5 text-red-500" />
                    }
                  />
                </FormItem>
              )}
            />

            <Controller
              name="address.city"
              control={form.control}
              render={({ field, fieldState }) => (
                <FormItem className="space-y-0 w-[170px]">
                  <FormLabel className="text-hw-neutral-700 text-xs font-normal dark:text-hw-neutral-300">
                    City<span className="text-red-500"></span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="City"
                      {...field}
                      value={field.value || ''}
                      className="w-[170px] h-[44px] border-[#E4E4E8]"
                      disabled={true}
                    />
                  </FormControl>
                  {/* ✅ Display error messages explicitly */}
                  {fieldState.error && (
                    <FormMessage className="m-0 text-xs flex items-center pt-1">
                      <ValidationErrorIcon className="h-3.5 w-3.5 text-red-500" />
                      {fieldState.error.message}
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />

            <Controller
              name="address.state"
              control={form.control}
              render={({ field, fieldState }) => (
                <FormItem className="space-y-0 w-[170px]">
                  <FormLabel className="text-hw-neutral-700 text-xs font-normal dark:text-hw-neutral-300">
                    State<span className="text-red-500"></span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="State"
                      {...field}
                      value={field.value || ''}
                      className="w-[170px] h-[44px] border-[#E4E4E8]"
                      disabled={true}
                    />
                  </FormControl>
                  {/* ✅ Display error messages explicitly */}
                  {fieldState.error && (
                    <FormMessage className="m-0 text-xs flex items-center pt-1">
                      <ValidationErrorIcon className="h-3.5 w-3.5 text-red-500" />
                      {fieldState.error.message}
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddEditAgentDetails;
