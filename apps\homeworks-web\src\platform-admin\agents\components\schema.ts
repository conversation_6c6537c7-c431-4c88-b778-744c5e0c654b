import { z } from 'zod';

export interface ZipCityState {
  zipCode: string;
  stateName: string;
  cityName: string;
}

export const formSchema = z.object({
  user: z.object({
    firstName: z
      .string()
      .trim()
      .min(1, 'Please enter first name')
      .max(20, `First name can't exceed 20 characters`)
      .transform((value) => value.trim()) // Trim spaces before validation
      .refine((value) => value.trim().length > 0, {
        message: 'First name cannot be empty',
      })
      .refine((value) => /^[a-zA-Z ]+$/.test(value), {
        message: 'First name can only contain letters and spaces',
      }),
    lastName: z
      .string()
      .trim()
      .min(1, 'Please enter last name')
      .max(20, `Last name can't exceed 20 characters`)
      .transform((value) => value.trim()) // Trim spaces before validation
      .refine((value) => value.trim().length > 0, {
        message: 'Last name cannot be empty',
      })
      .refine((value) => /^[a-zA-Z ]+$/.test(value), {
        message: 'Last name can only contain letters and spaces',
      }),
    email: z
      .string()
      .min(1, 'Please enter email id')
      .email({ message: 'Please enter valid email id' })
      .transform((value) => value.trim()) // Trim spaces before validation
      .refine((value) => value.trim().length > 0, {
        message: 'Email cannot be empty',
      }),
    username: z.string(),
    contactNumber: z
      .string()
      .trim()
      .min(1, { message: 'Please enter phone number' })
      .regex(/^\d+$/, { message: 'Phone number must contain only digits' })
      .refine((value) => value.length === 10, {
        message: 'Phone number must be exactly 10 digits',
      }),
    DOB: z.string().min(1, { message: 'Please select date of birth' }),
    profileImageUrl: z
      .union([
        // For File objects (new uploads)
        z
          .instanceof(File)
          .refine((file) => file.size > 0, {
            message: 'Please upload a valid profile photo',
          })
          .refine(
            (file) =>
              ['image/jpeg', 'image/png', 'image/jpg'].includes(file.type),
            {
              message: 'Only JPG, PNG, and JPEG files are allowed',
            },
          )
          .refine((file) => file.size <= 5 * 1024 * 1024, {
            message: 'Profile photo must be less than 5MB',
          }),

        // For existing files with URL
        z.object({
          url: z.string().url(),
          type: z.string().optional(),
          size: z.number().optional(),
        }),

        // For null/undefined values (optional)
        z.null(),
        z.undefined(),
        z.literal(''),
      ])
      .optional()
      .nullable(),
  }),

  address: z.object({
    street: z
      .string()
      .trim()
      .min(1, 'Please enter Street address')
      .max(50, `Street address can't exceed 50 characters`)
      .transform((value) => value.trim()) // Trim spaces before validation
      .refine((value) => value.trim().length > 0, {
        message: 'Street address cannot be empty',
      }),
    city: z.string().min(1, 'Please select city'),
    state: z.string().min(1, 'Please select state'),
    zipcode: z
      .string({ required_error: 'Please enter the zip code' })
      .trim()
      .min(1, 'Please enter zip code')
      .max(5, 'Zip code cannot exceed 5 digits')
      .transform((value) => value.trim()) // Trim spaces before validation
      .refine((value) => value.trim().length > 0, {
        message: 'Zip code cannot be empty',
      })
      .refine((value) => /^\d+$/.test(value), {
        message: 'Only numbers are allowed',
      }),
  }),

  agentExperienceOptionId: z.string()
  .min(1, 'Please select agent experience option'),

  licenseNumber: z
    .string()
    .trim()
    .min(1, 'Please enter license number')
    .max(15, `License number can't exceed 15 characters`)
    .transform((value) => value.trim()) // Trim spaces before validation
    .refine((value) => value.trim().length > 0, {
      message: 'License number cannot be empty',
    }),

  licenseIssuedState: z.string().min(1, 'Please select license issued state'),

  licenseExpiryDate: z.string().min(1, 'Please select license expiration date'),

  tinNumber: z
    .string()
    .min(9, 'TIN number must be 9 digits')
    .max(9, `TIN number can't exceed 9 characters`)
    .transform((value) => value.trim()) // Trim spaces before validation
    .refine((value) => value.trim().length > 0, {
      message: 'TIN number cannot be empty',
    })
    .refine((value) => /^\d+$/.test(value), {
      message: 'Only numbers are allowed',
    }),

  driverLicenseNumber: z
    .string()
    .trim()
    .max(15, `Driver's license number can't exceed 15 characters`)
    .transform((value) => value.trim())
    .refine(
      (value) => {
        return value === '' || value.length > 0;
      },
      {
        message: "Driver's license number cannot be empty",
      },
    )
    .refine((value) => value === '' || /^[a-zA-Z0-9]+$/.test(value), {
      message: 'Only letters and numbers are allowed',
    })
    .optional(),

  govDocumentTypeId: z
    .string()
    .min(1, 'Please select government document type'),

  addressProofDocumentTypeId: z.string().optional(),
  // File validations
  profLicenseDoc: z
    .union([
      // For File objects (new uploads)
      z
        .instanceof(File)
        .refine((file) => file.size > 0, {
          message: 'Please upload a valid professional license document',
        })
        .refine(
          (file) =>
            [
              'image/jpeg',
              'image/png',
              'image/jpg',
              'application/pdf',
            ].includes(file.type),
          {
            message: 'Only JPG, PNG, JPEG, and PDF files are allowed',
          },
        )
        .refine((file) => file.size <= 5 * 1024 * 1024, {
          message: 'Professional license document must be less than 5MB',
        }),

      // For existing files with URL
      z.object({
        url: z.string().url(),
        type: z.string().optional(),
        originalFilename: z.string().optional(),
        size: z.number().optional(),
      }),

      // For null/undefined values (should not happen as this is required)
      z.null(),
      z.undefined(),
    ])
    .refine((val) => val !== null && val !== undefined, {
      message: 'Please upload professional license document',
    }),

  govDocument: z
    .union([
      // For File objects (new uploads)
      z
        .instanceof(File)
        .refine((file) => file.size > 0, {
          message: 'Please upload a valid government id document',
        })
        .refine(
          (file) =>
            [
              'image/jpeg',
              'image/png',
              'image/jpg',
              'application/pdf',
            ].includes(file.type),
          {
            message: 'Only JPG, PNG, JPEG, and PDF files are allowed',
          },
        )
        .refine((file) => file.size <= 5 * 1024 * 1024, {
          message: 'Government id document must be less than 5MB',
        }),

      // For existing files with URL
      z.object({
        url: z.string().url(),
        type: z.string().optional(),
        originalFilename: z.string().optional(),
        size: z.number().optional(),
      }),

      // For null/undefined values (should not happen as this is required)
      z.null(),
      z.undefined(),
    ])
    .refine((val) => val !== null && val !== undefined, {
      message: 'Please upload government id document',
    }),

  addressProofDocument: z
    .any()
    .nullable()
    .optional()
    .superRefine((file, ctx) => {
      // Get the parent form data to access addressProofDocumentTypeId
      const addressProofTypeId = ctx.path.find(
        (p) => typeof p === 'string' && p === 'addressProofDocumentTypeId',
      );

      // If address proof type is selected, file becomes required
      if (addressProofTypeId && !file) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Please upload address proof document',
        });
        return;
      }

      // If file is provided, validate its type
      if (file && !file.url) {
        if (
          !['image/jpeg', 'image/png', 'application/pdf'].includes(file?.type)
        ) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Only JPG, PNG, JPEG, and PDF files are allowed',
          });
        }

        // Validate file size
        if (file?.size > 5 * 1024 * 1024) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Address proof document must be less than 5MB',
          });
        }
      }
    }),
});

export const initialValuesAddAgent = {
  user: {
    firstName: '',
    lastName: '',
    email: '',
    username: '',
    contactNumber: '',
    DOB: '',
  },
  address: {
    street: '',
    city: '',
    state: '',
    zipcode: '',
  },
  agentExperienceOptionId: '',
  licenseNumber: '',
  licenseIssuedState: '',
  licenseExpiryDate: '',
  tinNumber: '',
  driverLicenseNumber: '',
  govDocumentTypeId: '',
  addressProofDocumentTypeId: '',
  profLicenseDoc: undefined,
  govDocument: undefined,
  addressProofDocument: undefined,
};

export type FormSchemaType = z.infer<typeof formSchema>;
