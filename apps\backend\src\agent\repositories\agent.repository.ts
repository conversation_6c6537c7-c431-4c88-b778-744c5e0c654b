import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, DeepPartial, Repository } from 'typeorm';
import { AgentDetail } from '../entities/agent.entity';
import { CreateAgentDto } from '../dto/create-agent.dto';
import { UpdateAgentDto } from '../dto/update-agent.dto';
import { Identifier, UserType } from '@shared-types';
import { User } from '../../users/entities/user.entity';
import { UserRole } from '../../role/entities/user-role.entity';
import { AgentStatus } from '../entities/agent-status.entity';
import { Role } from '../../role/entities/role.entity';
import { MinioService } from '../../common/minio/minio.service';
import { Document } from '../../document/entities/document.entity';
import { Address } from '../../users/entities/address.entity';
import { GovDocumentType } from '../entities/gov-document-type.entity';
import { AddressProofDocumentType } from '../entities/address_proof_document_type.entity';
import { State } from '../../zip-codes/entities/state.entity';
import { MarketCenter } from '../../market-centers/entities/market-center.entity';
import { Account } from '../../accounts/entities/account.entity';
import { CommonService } from '../../common/utils/common.service';
import { EmailService } from '../../common/email/email.service';
import { AgentFilterDto } from '../dto/agent-filter.dto';
import { PageOptionsDto } from '../../common/pagination/pageOptions.dto';
import { SortSearchDataDto } from '../../common/pagination/sort-search-data.dto';
import { PageMetaDto } from '../../common/pagination/pageMeta.dto';
import { MarketCenterUser } from '../../market-centers/entities/market-center-user.entity';
import { ConfigService } from '@nestjs/config';
import { AgentExperienceOption } from '../entities/agent_experience_option.entity';

interface DocumentWithUrl extends Document {
  url: string;
  size?: number;
  download?: string;
}

@Injectable()
export class AgentRepository extends Repository<AgentDetail> {
  private readonly logger = new Logger(AgentRepository.name);

  constructor(
    @InjectRepository(AgentDetail)
    private readonly agentRepository: Repository<AgentDetail>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(UserRole)
    private readonly userRoleRepository: Repository<UserRole>,
    private readonly minioService: MinioService,
    private readonly commonService: CommonService,
    private readonly emailService: EmailService,
    private readonly configService: ConfigService,
    private dataSource: DataSource,
  ) {
    super(AgentDetail, dataSource.createEntityManager());
  }

  async uploadFiles(
    bucketName: string,
    file?: Express.Multer.File,
  ): Promise<any> {
    if (!file) {
      return null;
    }

    try {
      // Add await here
      const uploadResult = await this.minioService.uploadFile(file, bucketName);
      // Validate the upload result
      if (!uploadResult) {
        this.logger.error('Upload result is null or undefined');
        throw new Error('File upload failed - no result returned');
      }

      this.logger.log(`Upload successful: ${JSON.stringify(uploadResult)}`);
      return uploadResult;
    } catch (error) {
      this.logger.error(
        `Error uploading file ${file.originalname}: ${error.message}`,
      );
      throw new BadRequestException(`Error uploading file: ${error.message}`);
    }
  }

  async createAgent(createAgentDto: CreateAgentDto, managerUser?: UserType) {
    try {
      return await this.dataSource.transaction(
        async (transactionalEntityManager) => {
          // First, get the market center details
          let marketCenter;
          if (
            createAgentDto.marketCenterId &&
            createAgentDto?.marketCenterId !== undefined
          ) {
            marketCenter = await transactionalEntityManager.findOne(
              MarketCenter,
              {
                where: { id: createAgentDto.marketCenterId },
              },
            );

            if (!marketCenter) {
              throw new NotFoundException(
                `MarketCenter with ID ${createAgentDto.marketCenterId} not found`,
              );
            }

            // Get market center manager details through market_center_user
            const marketCenterUser = await transactionalEntityManager
              .createQueryBuilder(MarketCenterUser, 'mcu')
              .leftJoinAndSelect('mcu.userRole', 'userRole')
              .leftJoinAndSelect('userRole.user', 'user')
              .where('mcu."market_center_id" = :marketCenterId', {
                // Fixed column name
                marketCenterId: createAgentDto.marketCenterId,
              })
              .andWhere('mcu."is_active" = :isActive', { isActive: true })
              .getOne();

            if (!marketCenterUser) {
              throw new NotFoundException(
                `No active manager found for Market Center ID ${createAgentDto.marketCenterId}`,
              );
            }
          }

          // Validate govDocumentType exists
          const govDocumentType = await transactionalEntityManager.findOne(
            GovDocumentType,
            {
              where: { id: createAgentDto.govDocumentTypeId },
            },
          );
          if (!govDocumentType) {
            throw new NotFoundException(
              `GovDocumentType with ID ${createAgentDto.govDocumentTypeId} not found`,
            );
          }

          // Validate agentStatus exists
          const agentStatus = await transactionalEntityManager.findOne(
            AgentStatus,
            {
              where: { name: 'Active' },
            },
          );
          if (!agentStatus) {
            throw new NotFoundException(
              'Active agent status not found in the system',
            );
          }

          // Validate licenseIssuedState if provided
          if (createAgentDto.licenseIssuedStateId) {
            const state = await transactionalEntityManager.findOne(State, {
              where: { id: createAgentDto.licenseIssuedStateId },
            });
            if (!state) {
              throw new NotFoundException(
                `State with ID ${createAgentDto.licenseIssuedStateId} not found`,
              );
            }
          }

          // Validate addressProofDocumentType if provided
          if (createAgentDto.addressProofDocumentTypeId) {
            const addressProofDocType =
              await transactionalEntityManager.findOne(
                AddressProofDocumentType,
                {
                  where: { id: createAgentDto.addressProofDocumentTypeId },
                },
              );
            if (!addressProofDocType) {
              throw new NotFoundException(
                `AddressProofDocumentType with ID ${createAgentDto.addressProofDocumentTypeId} not found`,
              );
            }
          }

          // Validate marketCenter if provided
          if (
            createAgentDto.marketCenterId &&
            createAgentDto.marketCenterId !== undefined
          ) {
            const marketCenter = await transactionalEntityManager.findOne(
              MarketCenter,
              {
                where: { id: createAgentDto.marketCenterId },
              },
            );
            if (!marketCenter) {
              throw new NotFoundException(
                `MarketCenter with ID ${createAgentDto.marketCenterId} not found`,
              );
            }
          }

          // Validate account if provided
          if (createAgentDto.accountId) {
            const account = await transactionalEntityManager.findOne(Account, {
              where: { id: createAgentDto.accountId },
            });
            if (!account) {
              throw new NotFoundException(
                `Account with ID ${createAgentDto.accountId} not found`,
              );
            }
          }

          const { firstName, lastName, username, contactNumber, DOB, email } =
            createAgentDto.user;
          let user = await this.userRepository.findOneBy({ username });
          const { password, hashedPassword } =
            await this.commonService.generateSecurePassword();
          let userRole: UserRole;
         
          if (user) {
            if (user.isDeleted) {
         

              // If user is deleted then create new user
              user = transactionalEntityManager.create(User, {
                firstName,
                lastName,
                username,
                contactNumber,
                DOB: DOB,
                isDeleted: false,
                email: email,
                isActive: true,
                password : password
              });

              const savedUser = await transactionalEntityManager.save(
                User,
                user,
              );

              
              //create new role for agent user
              const agentRole = await transactionalEntityManager.findOne(Role, {
                where: { name: 'agent' },
              });
              if (!agentRole) {
                throw new NotFoundException(`Role 'agent' not found.`);
              }
              userRole = transactionalEntityManager.create(UserRole, {
                userId: savedUser.id,
                roleId: agentRole.id,
                isActive: true,
                isDeleted: false,
              });
              await transactionalEntityManager.save(UserRole, userRole);

              user = savedUser;
            } else {
              const agentRole = await transactionalEntityManager.findOne(Role, {
                where: { name: 'agent' },
              });
              if (!agentRole) {
                throw new NotFoundException(`Role 'agent' not found.`);
              }
              const existingUserRole = await this.userRoleRepository.findOneBy({
                userId: user.id,
                roleId: agentRole.id,
                isActive: true,
                isDeleted: false,
              });
              if (existingUserRole) {
                // Log only once and throw the error
                this.logger.error(`User ${username} is already an agent.`);
                throw new NotFoundException(
                  `User ${username} is already an agent.`,
                );
              }
              userRole = transactionalEntityManager.create(UserRole, {
                userId: user?.id,
                roleId: agentRole?.id,
                isActive: true,
                isDeleted: false,
              });
              await transactionalEntityManager.save(userRole);
            }
          } else {
            //if no user exists then create new agent user
            this.logger.log(`Creating new user for ${username}`);           
            user = transactionalEntityManager.create(User, {
              firstName,
              lastName,
              username,
              contactNumber,
              DOB: DOB,
              isActive: true,
              isDeleted: false,
              email: email,
              password : password
            });
            const savedUser = await transactionalEntityManager.save(user);

            const agentRole = await transactionalEntityManager.findOne(Role, {
              where: { name: 'agent' },
            });
            if (!agentRole) {
              throw new NotFoundException(`Role 'agent' not found.`);
            }
            userRole = transactionalEntityManager.create(UserRole, {
              userId: savedUser.id,
              roleId: agentRole.id,
              isActive: true,
              isDeleted: false,
            });
            await transactionalEntityManager.save(userRole);
            user = savedUser;
          }

          const accountName = await transactionalEntityManager.findOne(
            Account,
            {
              where: { id: createAgentDto?.accountId },
            },
          );
          const merketCenterName =
            createAgentDto.marketCenterId !== undefined
              ? marketCenter.name
              : accountName.name;
          try {
            await this.emailService.generateAndSendEmail({
              to: user.email,
              subject: `Welcome to ${merketCenterName} - Your HomeWorks Agent App Credentials`,
              templateName: 'AGENT_CREDENTIALS',
              templateData: {
                firstName: user.firstName,
                password: password,
                loginUrl: process.env.FRONTEND_URL,
                email: user.email,
                marketCenterName: merketCenterName,
                marketCenterManagerName: `${managerUser.firstName} ${managerUser.lastName}`,
                managerEmail: managerUser.email,
                managerPhone: managerUser.contactNumber,
                Google_Play_Store: process.env.AGENT_PLAYSTORE_APP_URL,
                App_Store_iOS: process.env.AGENT_APPSTORE_APP_URL,
                entityName:
                  createAgentDto.currentUserRole !== 'company_admin'
                    ? 'Market Center'
                    : 'Company',
                entityRole:
                  createAgentDto.currentUserRole !== 'company_admin'
                    ? 'Market Center Manager'
                    : 'Company Admin',
              },
            });
          } catch (error) {
            this.logger.error(
              `Failed to send password email to ${user.email}:`,
              error,
            );
          }

          let uploadedProfilePhoto: DocumentWithUrl | null = null;
          if (createAgentDto.user.profileImageUrl) {
            try {
              const fileNames = await this.uploadFiles(
                process.env.MINIO_DEFAULT_BUCKETS,
                createAgentDto.user.profileImageUrl as Express.Multer.File,
              );

              if (fileNames) {
                // Create Document entity
                const profilePhoto = transactionalEntityManager.create(
                  Document,
                  {
                    originalFilename:
                      createAgentDto.user.profileImageUrl['originalname'],
                    minioFilename: fileNames.minioFilename,
                    type: createAgentDto.user.profileImageUrl[
                      'mimetype'
                    ] as string,
                  },
                );
                // Save Document
                uploadedProfilePhoto = (await transactionalEntityManager.save(
                  Document,
                  profilePhoto,
                )) as DocumentWithUrl;

                // Add URL to make it match your desired format
                (uploadedProfilePhoto as DocumentWithUrl).url =
                  await this.minioService.getFileUrl(
                    uploadedProfilePhoto.minioFilename,
                    process.env.MINIO_DEFAULT_BUCKETS,
                    false,
                  );
                await transactionalEntityManager.update(
                  User,
                  { id: user.id },
                  {
                    profileImageUrl: uploadedProfilePhoto as unknown as string,
                  },
                );

                // Update the local user object to reflect changes
                user.profileImageUrl = uploadedProfilePhoto.url;
              }
            } catch (error) {
              this.logger.error(
                `Error uploading profile image: ${error.message}`,
              );
              throw new BadRequestException(
                `Error uploading profile image: ${error.message}`,
              );
            }
          }

          let uploadedGovDoc;
          if (createAgentDto.govDocument) {
            const fileNames = await this.uploadFiles(
              process.env.MINIO_DEFAULT_BUCKETS,
              createAgentDto.govDocument,
            );
            await this.minioService.getFileUrl(
              fileNames.minioFilename,
              process.env.MINIO_DEFAULT_BUCKETS,
            );
            const govDocument = transactionalEntityManager.create(Document, {
              originalFilename: createAgentDto.govDocument.originalname,
              minioFilename: fileNames.minioFilename,
              type: createAgentDto.govDocument.mimetype,
            });
            uploadedGovDoc = await transactionalEntityManager.save(govDocument);
            this.logger.log(`Successfully uploaded govDocument file`);
          }

          let uploadedAddressProofDoc;
          if (createAgentDto.addressProofDocument) {
            const fileNames = await this.uploadFiles(
              process.env.MINIO_DEFAULT_BUCKETS,
              createAgentDto.addressProofDocument,
            );
            await this.minioService.getFileUrl(
              fileNames.minioFilename,
              process.env.MINIO_DEFAULT_BUCKETS,
            );
            const addressProofDocument = transactionalEntityManager.create(
              Document,
              {
                originalFilename:
                  createAgentDto.addressProofDocument.originalname,
                minioFilename: fileNames.minioFilename,
                type: createAgentDto.addressProofDocument.mimetype,
              },
            );
            uploadedAddressProofDoc = await transactionalEntityManager.save(
              addressProofDocument,
            );
            this.logger.log(`Successfully uploaded addressProofDocument file`);
          }

          let uploadedProfLicenseDoc;
          if (createAgentDto.profLicenseDoc) {
            const fileNames = await this.uploadFiles(
              process.env.MINIO_DEFAULT_BUCKETS,
              createAgentDto.profLicenseDoc,
            );
            const profLicenseDoc = transactionalEntityManager.create(Document, {
              originalFilename: createAgentDto.profLicenseDoc.originalname,
              minioFilename: fileNames.minioFilename,
              type: createAgentDto.profLicenseDoc.mimetype,
            });
            uploadedProfLicenseDoc = await transactionalEntityManager.save(
              profLicenseDoc,
            );
            this.logger.log(`Successfully uploaded profLicenseDoc file`);
          }

          const addressData =
            typeof createAgentDto.address === 'string'
              ? JSON.parse(createAgentDto.address)
              : createAgentDto.address;

          const address = transactionalEntityManager.create(Address, {
            street: addressData.street,
            city: addressData.city,
            state: addressData.state,
            zipcode: addressData.zipcode,
          });
          this.logger.debug('Creating address with data:', address);
          const savedAddress = await transactionalEntityManager.save(
            Address,
            address,
          );
          const marketCenterDetails = await transactionalEntityManager
            .createQueryBuilder(MarketCenter, 'mc')
            .leftJoinAndSelect('mc.account', 'account')
            .where('mc.id = :id', { id: createAgentDto.marketCenterId })
            .getOne();

          if (
            !marketCenterDetails &&
            createAgentDto.marketCenterId !== undefined
          ) {
            throw new NotFoundException(
              `MarketCenter with ID ${createAgentDto.marketCenterId} not found`,
            );
          }
          const onboardedByRoleId = await this.userRoleRepository.findOneBy({
            userId: managerUser?.id,
            roleId: createAgentDto?.onboardedBy,
            isActive: true,
            isDeleted: false,
          });
          // Set account based on marketCenterDetails or accountId
          let accountId = null;
          if (createAgentDto.accountId) {
            accountId = createAgentDto.accountId;
          } else if (marketCenterDetails && marketCenterDetails.account) {
            accountId = marketCenterDetails.account.id;
          }

          // Create the agent entity with proper null handling
          const agentDetail = transactionalEntityManager.create(AgentDetail, {
            user: { id: user.id } as any,
            code: createAgentDto.code,
            address: { id: savedAddress.id },
            license_number: createAgentDto.license_number,
            licenseIssuedState: createAgentDto.licenseIssuedStateId
              ? { id: createAgentDto.licenseIssuedStateId }
              : null,
            license_exp_date: createAgentDto.license_exp_date,
            profLicenseDoc: uploadedProfLicenseDoc
              ? { id: uploadedProfLicenseDoc.id }
              : null,
            tin_number: createAgentDto.tin_number,
            driver_license_number: createAgentDto.driver_license_number || null,
            govDocumentType: { id: createAgentDto.govDocumentTypeId },
            addressProofDocumentType: createAgentDto.addressProofDocumentTypeId
              ? { id: createAgentDto.addressProofDocumentTypeId }
              : null,
            govDocument: uploadedGovDoc ? { id: uploadedGovDoc.id } : null,
            addressProofDocument: uploadedAddressProofDoc
              ? { id: uploadedAddressProofDoc.id }
              : null,
            agentStatus: agentStatus ? { id: agentStatus.id } : null,
            isActive: true,
            isDeleted: false,
            marketCenter:
              createAgentDto.marketCenterId &&
              createAgentDto.marketCenterId !== ''
                ? { id: createAgentDto.marketCenterId }
                : null,
            account: accountId ? { id: accountId } : null,
            onboardedBy: onboardedByRoleId ? onboardedByRoleId.id : null,
            agentExperienceOption: createAgentDto.agentExperienceOptionId
              ? Number(createAgentDto.agentExperienceOptionId)
              : null,
          });
          return await transactionalEntityManager.save(
            AgentDetail,
            agentDetail,
          );
        },
      );
    } catch (error) {
      this.logger.error(`Error creating agent: ${error.message}`);
      throw error;
    }
  }

  async findAgentById(id: Identifier): Promise<AgentDetail> {
    try {
      const agent = await this.agentRepository
        .createQueryBuilder('agent')
        .leftJoinAndSelect('agent.user', 'user')
        .leftJoinAndSelect('user.userRoles', 'userRoles')
        .leftJoinAndSelect('userRoles.role', 'role')
        .leftJoinAndSelect('agent.address', 'address')
        .leftJoinAndSelect('agent.licenseIssuedState', 'licenseIssuedState')
        .leftJoinAndSelect('agent.profLicenseDoc', 'profLicenseDoc')
        .leftJoinAndSelect('agent.govDocumentType', 'govDocumentType')
        .leftJoinAndSelect(
          'agent.addressProofDocumentType',
          'addressProofDocumentType',
        )
        .leftJoinAndSelect('agent.govDocument', 'govDocument')
        .leftJoinAndSelect('agent.addressProofDocument', 'addressProofDocument')
        .leftJoinAndSelect('agent.agentStatus', 'agentStatus')
        .leftJoinAndSelect('agent.marketCenter', 'marketCenter')
        .leftJoinAndSelect('agent.account', 'account')
        .leftJoinAndSelect(
          'agent.agentExperienceOption',
          'agentExperienceOption',
        )
        .where('agent.id = :id', { id })
        // .andWhere('agent.isDeleted = :isDeleted', { isDeleted: false })
        .getOne();

      if (!agent) {
        this.logger.error(`Agent with ID ${id} not found`);
        throw new NotFoundException(`Agent with ID ${id} not found`);
      }

      // Optional: Transform document URLs if needed
      if (agent.govDocument) {
        try {
          (agent.govDocument as DocumentWithUrl).url =
            await this.minioService.getFileUrl(
              agent.govDocument.minioFilename,
              process.env.MINIO_DEFAULT_BUCKETS,
              false,
            );
        } catch (error) {
          this.logger.warn(
            `Error getting URL for govDocument: ${error.message}`,
          );
        }
      }

      if (agent.addressProofDocument) {
        try {
          (agent.addressProofDocument as DocumentWithUrl).url =
            await this.minioService.getFileUrl(
              agent.addressProofDocument.minioFilename,
              process.env.MINIO_DEFAULT_BUCKETS,
              false,
            );
        } catch (error) {
          this.logger.warn(
            `Error getting URL for addressProofDocument: ${error.message}`,
          );
        }
      }

      if (agent.profLicenseDoc) {
        try {
          (agent.profLicenseDoc as DocumentWithUrl).url =
            await this.minioService.getFileUrl(
              agent.profLicenseDoc.minioFilename,
              process.env.MINIO_DEFAULT_BUCKETS,
              false,
            );
        } catch (error) {
          this.logger.warn(
            `Error getting URL for profLicenseDoc: ${error.message}`,
          );
        }
      }

      if (agent.user && agent.user.userRoles) {
        agent.user['roles'] = agent.user.userRoles.map((userRole) => ({
          ...userRole.role,
          userRoleId: userRole.id,
        }));
      }

      return agent;
    } catch (error) {
      this.logger.error(`Error finding agent with ID ${id}: ${error.message}`);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new NotFoundException(
        `Error retrieving agent details: ${error.message}`,
      );
    }
  }

  async findAgentByCode(code: string): Promise<AgentDetail> {
    return await this.agentRepository.findOne({
      where: { code },
      relations: [
        'user',
        'address',
        'licenseIssuedState',
        'profLicenseDoc',
        'govDocumentType',
        'addressProofDocumentType',
        'govDocument',
        'addressProofDocument',
        'agentStatus',
        'marketCenter',
        'account',
      ],
    });
  }

  async updateAgent(
    id: Identifier,
    updateAgentDto: UpdateAgentDto,
  ): Promise<AgentDetail> {
    try {
      return await this.manager.transaction(
        async (transactionalEntityManager) => {
          const existingAgent = await this.findAgentById(id);
          if (!existingAgent) {
            throw new NotFoundException(`Agent With ID ${id} not found`);
          }

          if (
            updateAgentDto.user &&
            updateAgentDto.user.email &&
            existingAgent.user &&
            existingAgent.user.email.toLowerCase() !==
              updateAgentDto.user.email.toLowerCase()
          ) {
            // Check if the new email belongs to any other user
            const agentWithSameEmail = await transactionalEntityManager
              .createQueryBuilder(AgentDetail, 'agent')
              .innerJoin('agent.user', 'user')
              .where('LOWER(user.email) = LOWER(:email)', {
                email: updateAgentDto.user.email,
              })
              .andWhere('agent.isDeleted = :isDeleted', { isDeleted: false })
              .andWhere('agent.id != :currentAgentId', { currentAgentId: id })
              .getOne();

            // If another agent with this email exists, throw an error
            if (agentWithSameEmail) {
              throw new BadRequestException(
                `Another agent with email ${updateAgentDto.user.email} already exists`,
              );
            }
          }

          // Check if any other agent already has this email
          const agentWithSameEmail = await transactionalEntityManager
            .createQueryBuilder(AgentDetail, 'agent')
            .innerJoin('agent.user', 'user')
            .where('LOWER(user.email) = LOWER(:email)', {
              email: updateAgentDto.user.email,
            })
            .andWhere('agent.isDeleted = :isDeleted', { isDeleted: false })
            .andWhere('agent.id != :currentAgentId', { currentAgentId: id })
            .getOne();

          // If another agent with this email exists, throw an error
          if (agentWithSameEmail) {
            throw new BadRequestException(
              `Another agent with email ${updateAgentDto.user.email} already exists`,
            );
          }

          // Handle address proof document deletion first, as a separate operation
          if (
            !updateAgentDto.addressProofDocumentTypeId &&
            existingAgent.addressProofDocument
          ) {
            try {
              this.logger.log(
                'Deleting address proof document due to empty document type',
              );

              // Store document info before removing references
              const documentId = existingAgent.addressProofDocument.id;
              const minioFilename =
                existingAgent.addressProofDocument.minioFilename;

              // Clear the references in the agent entity
              await transactionalEntityManager
                .createQueryBuilder()
                .update(AgentDetail)
                .set({
                  addressProofDocumentType: null,
                  addressProofDocument: null,
                })
                .where('id = :id', { id })
                .execute();

              // Delete the file from MinIO
              if (minioFilename) {
                await this.minioService.deleteFile(
                  minioFilename,
                  process.env.MINIO_DEFAULT_BUCKETS,
                );
              }

              // Delete the document record
              if (documentId) {
                await transactionalEntityManager
                  .createQueryBuilder()
                  .delete()
                  .from(Document)
                  .where('id = :id', { id: documentId })
                  .execute();
              }

              // Refresh our local copy of the agent
              existingAgent.addressProofDocument = null;
              existingAgent.addressProofDocumentType = null;

              this.logger.log(
                `Successfully deleted address proof document for agent ${id}`,
              );
            } catch (error) {
              this.logger.error(
                `Error deleting address proof document: ${error.message}`,
              );
              throw error; // Rethrow to fail the transaction
            }
          }

          // Handle profile image deletion
          if (!updateAgentDto.user?.profileImageUrl) {
            try {
              this.logger.log('Deleting profile image');

              // If profileImageUrl is a string that might be a JSON string
              let profileImageData = null;
              let minioFilename = null;
              let documentId = null;

              if (typeof existingAgent.user.profileImageUrl === 'string') {
                try {
                  // Try to parse it as JSON
                  profileImageData = JSON.parse(
                    existingAgent.user.profileImageUrl,
                  );
                  if (profileImageData && profileImageData.minioFilename) {
                    minioFilename = profileImageData.minioFilename;
                  }
                  if (profileImageData && profileImageData.id) {
                    documentId = profileImageData.id;
                  }
                } catch (e) {
                  // Not JSON, might be a direct URL or other string
                  this.logger.log('Profile image URL is not in JSON format');
                }
              } else if (
                existingAgent.user.profileImageUrl &&
                typeof existingAgent.user.profileImageUrl === 'object'
              ) {
                // It's already an object
                profileImageData = existingAgent.user.profileImageUrl;
                if (profileImageData.minioFilename) {
                  minioFilename = profileImageData.minioFilename;
                }
                if (profileImageData.id) {
                  documentId = profileImageData.id;
                }
              }

              // First update the user to remove the profile image reference
              await transactionalEntityManager.update(
                User,
                existingAgent.user.id,
                {
                  profileImageUrl: null,
                },
              );

              // Delete the file from MinIO if we have a filename
              if (minioFilename) {
                await this.minioService.deleteFile(
                  minioFilename,
                  process.env.MINIO_DEFAULT_BUCKETS,
                );
                this.logger.log(`Deleted profile image file: ${minioFilename}`);
              }

              // Delete the document record if we have an ID
              if (documentId) {
                await transactionalEntityManager
                  .createQueryBuilder()
                  .delete()
                  .from(Document)
                  .where('id = :id', { id: documentId })
                  .execute();
                this.logger.log(
                  `Deleted profile image document record: ${documentId}`,
                );
              }

              // Update our local reference
              existingAgent.user.profileImageUrl = null;

              this.logger.log(
                `Successfully deleted profile image for user ${existingAgent.user.id}`,
              );
            } catch (error) {
              this.logger.error(
                `Error deleting profile image: ${error.message}`,
              );
              // Don't throw here - we want to continue with the update even if image deletion fails
            }
          }
          // Check for duplicate email
          if (
            updateAgentDto.user &&
            'username' in updateAgentDto.user &&
            updateAgentDto.user.username !== existingAgent.user.username
          ) {
            // Check for duplicate email
            const duplicateEmail = await transactionalEntityManager.findOne(
              User,
              {
                where: {
                  email: updateAgentDto.user.username,
                  isDeleted: false,
                },
              },
            );
            if (duplicateEmail && duplicateEmail.id !== existingAgent.user.id) {
              throw new BadRequestException(`Email already exists`);
            }
          }

          // Handle file uploads
          let uploadedProfLicenseDoc = existingAgent.profLicenseDoc;
          let uploadedAddressProofDoc = existingAgent.addressProofDocument;
          let uploadedGovProofDoc = existingAgent.govDocument;
          let uploadedProfilePhoto = existingAgent.user.profileImageUrl;

          // Professional License Document
          if (updateAgentDto.profLicenseDoc) {
            try {
              const fileNames = await this.uploadFiles(
                process.env.MINIO_DEFAULT_BUCKETS,
                updateAgentDto.profLicenseDoc,
              );
              if (fileNames?.minioFilename) {
                const profLicenseDoc = transactionalEntityManager.create(
                  Document,
                  {
                    originalFilename:
                      updateAgentDto.profLicenseDoc.originalname,
                    minioFilename: fileNames.minioFilename,
                    type: updateAgentDto.profLicenseDoc.mimetype,
                  },
                );
                uploadedProfLicenseDoc = await transactionalEntityManager.save(
                  Document,
                  profLicenseDoc,
                );
                this.logger.log(
                  `Successfully uploaded new profLicenseDoc file`,
                );
              }
            } catch (error) {
              this.logger.error(
                `Error uploading profLicenseDoc: ${error.message}`,
              );
              throw new BadRequestException(
                `Error uploading professional license document`,
              );
            }
          }

          // Address Proof Document
          if (updateAgentDto.addressProofDocument) {
            try {
              const fileNames = await this.uploadFiles(
                process.env.MINIO_DEFAULT_BUCKETS,
                updateAgentDto.addressProofDocument,
              );
              if (fileNames) {
                const addressProofDoc = transactionalEntityManager.create(
                  Document,
                  {
                    originalFilename:
                      updateAgentDto.addressProofDocument.originalname,
                    minioFilename: fileNames.minioFilename,
                    type: updateAgentDto.addressProofDocument.mimetype,
                  },
                );
                uploadedAddressProofDoc = await transactionalEntityManager.save(
                  Document,
                  addressProofDoc,
                );
                this.logger.log(
                  `Successfully uploaded new addressProofDocument file`,
                );
              }
            } catch (error) {
              this.logger.error(
                `Error uploading addressProofDocument: ${error.message}`,
              );
              throw new BadRequestException(
                `Error uploading address proof document`,
              );
            }
          }

          // Government Document
          if (updateAgentDto.govDocument) {
            try {
              const fileNames = await this.uploadFiles(
                process.env.MINIO_DEFAULT_BUCKETS,
                updateAgentDto.govDocument,
              );
              if (fileNames) {
                const govDocument = transactionalEntityManager.create(
                  Document,
                  {
                    originalFilename: updateAgentDto.govDocument.originalname,
                    minioFilename: fileNames.minioFilename,
                    type: updateAgentDto.govDocument.mimetype,
                  },
                );
                uploadedGovProofDoc = await transactionalEntityManager.save(
                  Document,
                  govDocument,
                );
                this.logger.log(`Successfully uploaded new govDocument file`);
              }
            } catch (error) {
              this.logger.error(
                `Error uploading govDocument: ${error.message}`,
              );
              throw new BadRequestException(
                `Error uploading government document`,
              );
            }
          }

          // Update related entities
          if (updateAgentDto.address) {
            await transactionalEntityManager.update(
              Address,
              existingAgent.address.id,
              updateAgentDto.address,
            );
          }
          if (updateAgentDto.user) {
            if (
              updateAgentDto.user?.profileImageUrl &&
              typeof updateAgentDto.user.profileImageUrl !== 'string' &&
              updateAgentDto.user.profileImageUrl instanceof Object &&
              'originalname' in updateAgentDto.user.profileImageUrl
            ) {
              try {
                const fileNames = await this.uploadFiles(
                  process.env.MINIO_DEFAULT_BUCKETS,
                  updateAgentDto.user
                    .profileImageUrl as unknown as Express.Multer.File,
                );
                if (fileNames) {
                  // Create Document entity
                  const profilePhoto = transactionalEntityManager.create(
                    Document,
                    {
                      originalFilename:
                        updateAgentDto.user.profileImageUrl['originalname'],
                      minioFilename: fileNames.minioFilename,
                      type: updateAgentDto.user.profileImageUrl[
                        'mimetype'
                      ] as string,
                    },
                  );
                  // Save Document - this will generate the id
                  uploadedProfilePhoto = (await transactionalEntityManager.save(
                    Document,
                    profilePhoto,
                  )) as unknown as string;

                  // Add URL to make it match your desired format
                  (uploadedProfilePhoto as unknown as DocumentWithUrl).url =
                    await this.minioService.getFileUrl(
                      (uploadedProfilePhoto as unknown as Document)
                        .minioFilename,
                      process.env.MINIO_DEFAULT_BUCKETS,
                      false,
                    );
                }
              } catch (error) {
                this.logger.error(
                  `Error uploading govDocument: ${error.message}`,
                );
                throw new BadRequestException(`Error uploading profile photo`);
              }
            }
            await transactionalEntityManager.update(
              User,
              existingAgent.user.id,
              { ...updateAgentDto.user, profileImageUrl: uploadedProfilePhoto },
            );
          }

          // Prepare updated agent data
          const updatedAgentData = {
            ...existingAgent,
            user: updateAgentDto.user || existingAgent.user,
            address: updateAgentDto.address || existingAgent.address,
            license_number:
              updateAgentDto.license_number || existingAgent.license_number,
            licenseIssuedState: updateAgentDto.licenseIssuedStateId
              ? { id: updateAgentDto.licenseIssuedStateId }
              : existingAgent.licenseIssuedState,
            license_exp_date:
              updateAgentDto.license_exp_date || existingAgent.license_exp_date,
            profLicenseDoc: uploadedProfLicenseDoc,
            tin_number: updateAgentDto.tin_number || existingAgent.tin_number,
            driver_license_number:
              updateAgentDto.driver_license_number ||
              existingAgent.driver_license_number,
            govDocumentType: updateAgentDto.govDocumentTypeId
              ? { id: updateAgentDto.govDocumentTypeId }
              : existingAgent.govDocumentType,
            govDocument: uploadedGovProofDoc,
            addressProofDocumentType: updateAgentDto.addressProofDocumentTypeId
              ? { id: updateAgentDto.addressProofDocumentTypeId }
              : null,
            addressProofDocument: uploadedAddressProofDoc,
            agentExperienceOption: updateAgentDto.agentExperienceOptionId
              ? { id: Number(updateAgentDto.agentExperienceOptionId) } // Convert to number if it's a string
              : existingAgent.agentExperienceOption,
          };

          // Save updated agent
          const savedAgent = await transactionalEntityManager.save(
            AgentDetail,
            {
              ...updatedAgentData,
              user: {
                ...updatedAgentData.user,
                profileImageUrl: updateAgentDto.user?.profileImageUrl
                  ? typeof updateAgentDto.user.profileImageUrl === 'string'
                    ? updateAgentDto.user.profileImageUrl
                    : uploadedProfilePhoto
                  : existingAgent.user.profileImageUrl, // Keep existing profile URL if no new upload
              },
            } as DeepPartial<AgentDetail>,
          );
          // Return updated agent with all relations
          return await this.findAgentById(savedAgent.id);
        },
      );
    } catch (error) {
      this.logger.error(`Error updating agent: ${error.message}`);
      throw error;
    }
  }

  async findAllAgents(
    pageOptionsDto: PageOptionsDto,
    sortSearchDataDto: SortSearchDataDto,
    filters: AgentFilterDto,
    marketCenterId?: string,
    accountId?: string,
  ) {
    try {
      const page = Math.max(1, pageOptionsDto.page || 1);
      const limit = Math.max(1, pageOptionsDto.limit || 10);
      const query = this.createQueryBuilder('agent')
        .leftJoinAndSelect('agent.user', 'user')
        .leftJoinAndSelect('agent.address', 'address')
        .leftJoinAndSelect('agent.agentStatus', 'agentStatus')
        .leftJoinAndSelect('agent.marketCenter', 'marketCenter')
        .leftJoinAndSelect('agent.account', 'account') // Make sure account is joined
        .leftJoinAndSelect('agent.licenseIssuedState', 'licenseIssuedState')
        .leftJoinAndSelect('agent.onboardedBy', 'onboardedBy')
        .leftJoinAndSelect('onboardedBy.user', 'onboardedByUser')
        .leftJoinAndSelect('onboardedBy.role', 'onboardedByRole')
        .leftJoinAndSelect(
          'marketCenter.marketCenterUsers',
          'marketCenterUsers',
          'marketCenterUsers.isDeleted = false',
        )
        .leftJoinAndSelect(
          'marketCenterUsers.userRole',
          'userRole',
          'userRole.isActive = true',
        )
        .leftJoinAndSelect('userRole.role', 'role')
        .leftJoinAndSelect('userRole.user', 'mcUser')
        .leftJoinAndSelect('agent.profLicenseDoc', 'profLicenseDoc')
        .leftJoinAndSelect('agent.govDocument', 'govDocument')
        .leftJoinAndSelect('agent.addressProofDocument', 'addressProofDocument')
        .leftJoinAndSelect('agent.govDocumentType', 'govDocumentType')
        .leftJoinAndSelect(
          'agent.addressProofDocumentType',
          'addressProofDocumentType',
        )
        .leftJoinAndSelect(
          'agent.agentExperienceOption',
          'agentExperienceOption',
        )
        .where('agent.isDeleted = false');

      // Apply hierarchical filtering
      if (marketCenterId) {
        // If marketCenterId is provided, filter by marketCenter regardless of accountId
        query.andWhere('marketCenter.id = :marketCenterId', {
          marketCenterId: marketCenterId,
        });
      } else if (accountId) {
        // If only accountId is provided (no marketCenterId), filter by account
        query.andWhere('account.id = :accountId', {
          accountId: accountId,
        });
      }

      // Apply filters
      if (sortSearchDataDto.searchText) {
        const searchText = sortSearchDataDto.searchText.trim();

        // Split search text into words for multi-word search
        const searchTerms = searchText
          .split(/\s+/)
          .filter((term) => term.length > 0);

        if (searchTerms.length === 1) {
          // Single word search - search in first name OR last name
          query.andWhere(
            '(LOWER(user.firstName) LIKE LOWER(:searchText) OR LOWER(user.lastName) LIKE LOWER(:searchText))',
            { searchText: `%${searchText}%` },
          );
        } else if (searchTerms.length > 1) {
          // Multi-word search - try different combinations
          query.andWhere(
            '(' +
              // Option 1: First term in firstName, second term in lastName
              '(LOWER(user.firstName) LIKE LOWER(:firstTerm) AND LOWER(user.lastName) LIKE LOWER(:secondTerm))' +
              ' OR ' +
              // Option 2: Second term in firstName, first term in lastName
              '(LOWER(user.firstName) LIKE LOWER(:secondTerm) AND LOWER(user.lastName) LIKE LOWER(:firstTerm))' +
              ' OR ' +
              // Option 3: Full search text in firstName
              'LOWER(user.firstName) LIKE LOWER(:fullSearch)' +
              ' OR ' +
              // Option 4: Full search text in lastName
              'LOWER(user.lastName) LIKE LOWER(:fullSearch)' +
              ' OR ' +
              // Option 5: Concatenated name search
              "LOWER(CONCAT(user.firstName, ' ', user.lastName)) LIKE LOWER(:fullSearchWithSpace)" +
              ')',
            {
              firstTerm: `%${searchTerms[0]}%`,
              secondTerm: `%${searchTerms[1]}%`,
              fullSearch: `%${searchText}%`,
              fullSearchWithSpace: `%${searchText}%`,
            },
          );
        }
      }

      if (filters.isActive !== undefined) {
        query.andWhere('agent.isActive = :isActive', {
          isActive: filters.isActive,
        });
      }

      if (filters.companyName) {
        query.andWhere('LOWER(account.name) LIKE LOWER(:companyName)', {
          companyName: `%${filters.companyName}%`,
        });
      }

      if (filters.marketCenterName) {
        query.andWhere(
          'LOWER(marketCenter.name) LIKE LOWER(:marketCenterName)',
          {
            marketCenterName: `%${filters.marketCenterName}%`,
          },
        );
      }

      if (filters.licenseNumber) {
        query.andWhere('agent.license_number = :licenseNumber', {
          licenseNumber: filters.licenseNumber,
        });
      }

      if (filters.tinNumber) {
        query.andWhere('agent.tin_number = :tinNumber', {
          tinNumber: filters.tinNumber,
        });
      }

      if (filters.zipCode) {
        query.andWhere('address.zipcode = :zipCode', {
          zipCode: filters.zipCode,
        });
      }

      if (filters.city && filters.city.length > 0) {
        query.andWhere('address.city IN (:...city)', { city: filters.city });
      }

      if (filters.state && filters.state.length > 0) {
        query.andWhere('address.state IN (:...state)', {
          state: filters.state,
        });
      }

      if (filters.startDate && filters.endDate) {
        query.andWhere('agent.createdAt BETWEEN :startDate AND :endDate', {
          startDate: filters.startDate,
          endDate: filters.endDate,
        });
      }

      if (
        'ids' in filters &&
        Array.isArray(filters.ids) &&
        filters.ids.length > 0
      ) {
        query.andWhere('agent.id IN (:...ids)', { ids: filters.ids });
      }

      // Get total count without any filters for totalRecordsWithoutFilters
      let totalAgentsQuery = this.createQueryBuilder('agent')
        .leftJoin('agent.user', 'user')
        .leftJoin('agent.marketCenter', 'marketCenter')
        .leftJoin('agent.account', 'account')
        .where('user.isDeleted = :isDeleted', { isDeleted: false });

      // Apply hierarchical filtering to totalAgentsQuery
      if (marketCenterId) {
        // If marketCenterId is provided, filter by marketCenter regardless of accountId
        totalAgentsQuery = totalAgentsQuery.andWhere(
          'marketCenter.id = :marketCenterId',
          {
            marketCenterId: marketCenterId,
          },
        );
      } else if (accountId) {
        // If only accountId is provided (no marketCenterId), filter by account
        totalAgentsQuery = totalAgentsQuery.andWhere(
          'account.id = :accountId',
          {
            accountId: accountId,
          },
        );
      }

      const totalAgents = await totalAgentsQuery.getCount();

      // Get results without applying sorting in the database query
      const [agents, total] = await query.getManyAndCount();

      // Apply sorting in memory after fetching the data
      const sortColumn = sortSearchDataDto.sortColumn || 'createdAt';
      const sortOrder = sortSearchDataDto.sortOrder || 'ASC';

      let sortedAgents = [...agents];

      if (sortColumn === 'name') {
        sortedAgents.sort((a, b) => {
          const nameA = `${a.user?.firstName || ''} ${
            a.user?.lastName || ''
          }`.toLowerCase();
          const nameB = `${b.user?.firstName || ''} ${
            b.user?.lastName || ''
          }`.toLowerCase();

          return sortOrder === 'ASC'
            ? nameA.localeCompare(nameB)
            : nameB.localeCompare(nameA);
        });
      } else if (sortColumn === 'email') {
        // Sort by email
        sortedAgents.sort((a, b) => {
          const emailA = a.user?.email?.toLowerCase() || '';
          const emailB = b.user?.email?.toLowerCase() || '';

          return sortOrder === 'ASC'
            ? emailA.localeCompare(emailB)
            : emailB.localeCompare(emailA);
        });
      } else if (sortColumn === 'createdAt') {
        // Sort by createdAt date
        sortedAgents.sort((a, b) => {
          const dateA = new Date(a.createdAt).getTime();
          const dateB = new Date(b.createdAt).getTime();

          return sortOrder === 'ASC' ? dateA - dateB : dateB - dateA;
        });
      }

      const startIndex = (page - 1) * limit;
      const paginatedAgents = sortedAgents.slice(
        startIndex,
        startIndex + limit,
      );

      const pageOptionsForDto = {
        skip: page,
        limit: limit,
      };

      // Process all documents with Promise.all to properly wait for all async operations
      await Promise.all(
        agents.map(async (item) => {
          // Process government document
          if (item.govDocument) {
            try {
              (item.govDocument as DocumentWithUrl).url =
                await this.minioService.getFileUrl(
                  item.govDocument.minioFilename,
                  process.env.MINIO_DEFAULT_BUCKETS,
                  false,
                );

              // Add download URL
              (item.govDocument as DocumentWithUrl).download =
                await this.minioService.getFileUrl(
                  item.govDocument.minioFilename,
                  process.env.MINIO_DEFAULT_BUCKETS,
                  true, // Force download
                );

              // Add file size
              try {
                (item.govDocument as DocumentWithUrl).size =
                  await this.minioService.getFileSize(
                    item.govDocument.minioFilename,
                    process.env.MINIO_DEFAULT_BUCKETS,
                  );
              } catch (sizeError) {
                this.logger.warn(
                  `Error getting file size for govDocument: ${sizeError.message}`,
                );
                (item.govDocument as DocumentWithUrl).size = 1024 * 1024; // Default to 1MB
              }
            } catch (error) {
              this.logger.warn(
                `Error getting URL for govDocument: ${error.message}`,
              );
            }
          }

          // Process address proof document
          if (item.addressProofDocument) {
            try {
              (item.addressProofDocument as DocumentWithUrl).url =
                await this.minioService.getFileUrl(
                  item.addressProofDocument.minioFilename,
                  process.env.MINIO_DEFAULT_BUCKETS,
                  false,
                );

              // Add download URL
              (item.addressProofDocument as DocumentWithUrl).download =
                await this.minioService.getFileUrl(
                  item.addressProofDocument.minioFilename,
                  process.env.MINIO_DEFAULT_BUCKETS,
                  true, // Force download
                );

              // Add file size
              try {
                (item.addressProofDocument as DocumentWithUrl).size =
                  await this.minioService.getFileSize(
                    item.addressProofDocument.minioFilename,
                    process.env.MINIO_DEFAULT_BUCKETS,
                  );
              } catch (sizeError) {
                this.logger.warn(
                  `Error getting file size for addressProofDocument: ${sizeError.message}`,
                );
                (item.addressProofDocument as DocumentWithUrl).size =
                  1024 * 1024; // Default to 1MB
              }
            } catch (error) {
              this.logger.warn(
                `Error getting URL for addressProofDocument: ${error.message}`,
              );
            }
          }

          // Process professional license document
          if (item.profLicenseDoc) {
            try {
              (item.profLicenseDoc as DocumentWithUrl).url =
                await this.minioService.getFileUrl(
                  item.profLicenseDoc.minioFilename,
                  process.env.MINIO_DEFAULT_BUCKETS,
                  false,
                );

              // Add download URL
              (item.profLicenseDoc as DocumentWithUrl).download =
                await this.minioService.getFileUrl(
                  item.profLicenseDoc.minioFilename,
                  process.env.MINIO_DEFAULT_BUCKETS,
                  true, // Force download
                );

              // Add file size
              try {
                (item.profLicenseDoc as DocumentWithUrl).size =
                  await this.minioService.getFileSize(
                    item.profLicenseDoc.minioFilename,
                    process.env.MINIO_DEFAULT_BUCKETS,
                  );
              } catch (sizeError) {
                this.logger.warn(
                  `Error getting file size for profLicenseDoc: ${sizeError.message}`,
                );
                (item.profLicenseDoc as DocumentWithUrl).size = 1024 * 1024; // Default to 1MB
              }
            } catch (error) {
              this.logger.warn(
                `Error getting URL for profLicenseDoc: ${error.message}`,
              );
            }
          }
        }),
      );

      // Create meta data for pagination
      const meta = new PageMetaDto({
        pageOptionsDto: pageOptionsForDto,
        itemCount: total,
        totalRecords: total,
        totalRecordsWithoutFilters: totalAgents,
      });

      // Return a plain object with the sorted and paginated data
      return {
        data: paginatedAgents,
        meta: meta,
      };
    } catch (error) {
      this.logger.error('Error in findAllAgents:', error);
      throw error;
    }
  }

  async findAgentsByMarketCenter(
    marketCenterId: Identifier,
  ): Promise<AgentDetail[]> {
    return await this.agentRepository.find({
      where: { marketCenter: { id: marketCenterId } },
      relations: ['user', 'agentStatus', 'marketCenter'],
    });
  }

  async findAgentsByAccount(accountId: Identifier): Promise<AgentDetail[]> {
    return await this.agentRepository.find({
      where: { account: { id: accountId } },
      relations: ['user', 'agentStatus', 'marketCenter'],
    });
  }

  async findAllGovDocuments(): Promise<GovDocumentType[]> {
    try {
      return await this.dataSource.getRepository(GovDocumentType).find();
    } catch (error) {
      this.logger.error(`Error in findAllGovDocuments: ${error.message}`);
      throw error;
    }
  }

  async findAllAddressProofDocuments(): Promise<AddressProofDocumentType[]> {
    try {
      return await this.dataSource
        .getRepository(AddressProofDocumentType)
        .find();
    } catch (error) {
      this.logger.error(`Error in findAllAddressProofDocs: ${error.message}`);
    }
  }

  async changeAgentStatus(id: string, isActive: boolean): Promise<AgentDetail> {
    return this.manager.transaction(async (transactionalEntityManager) => {
      // Find the agent with associated user
      const agent = await transactionalEntityManager.findOne(AgentDetail, {
        where: { id },
        relations: ['user', 'user.userRoles'],
      });

      if (!agent) {
        throw new NotFoundException('Agent not found');
      }

      // Update agent status
      agent.isActive = isActive;
      const updatedAgent = await transactionalEntityManager.save(
        AgentDetail,
        agent,
      );

      // Update associated user status
      if (agent.user) {
        agent.user.isActive = isActive;
        await transactionalEntityManager.save(User, agent.user);

        // Update user roles status
        if (agent.user.userRoles) {
          for (const userRole of agent.user.userRoles) {
            userRole.isActive = isActive;
            await transactionalEntityManager.save(UserRole, userRole);
          }
        }
      }

      return updatedAgent;
    });
  }

  async softDeleteAgent(id: Identifier): Promise<AgentDetail> {
    return this.manager.transaction(async (transactionalEntityManager) => {
      // Find the agent with associated user and user roles
      const agent = await transactionalEntityManager.findOne(AgentDetail, {
        where: { id, isDeleted: false },
        relations: ['user', 'user.userRoles', 'user.userRoles.role'],
      });

      if (!agent) {
        throw new NotFoundException('Agent not found');
      }

      // Soft delete the agent
      await transactionalEntityManager.update(AgentDetail, id, {
        isDeleted: true,
        isActive: false,
      });

      if (agent.user) {
        // Find the agent role for this user
        const agentUserRole = agent.user.userRoles.find(
          (ur) => ur.role?.name === 'agent',
        );

        if (agentUserRole) {
          // Check if user has other active roles
          const activeRoleCount = await transactionalEntityManager
            .createQueryBuilder(UserRole, 'ur')
            .where('ur.userId = :userId', {
              userId: agent.user.id,
            })
            .andWhere('ur.isDeleted = false')
            .getCount();

          // If user only has agent role, soft delete both user and role
          if (activeRoleCount === 1) {
            await transactionalEntityManager.update(User, agent.user.id, {
              isDeleted: true,
              isActive: false,
            });

            await transactionalEntityManager.update(
              UserRole,
              agentUserRole.id,
              {
                isDeleted: true,
                isActive: false,
              },
            );
          }
          // If user has multiple roles, only soft delete the agent role
          else if (activeRoleCount > 1) {
            await transactionalEntityManager.update(
              UserRole,
              agentUserRole.id,
              {
                isDeleted: true,
                isActive: false,
              },
            );
          }
        }
      }

      return await transactionalEntityManager.findOne(AgentDetail, {
        where: { id },
        relations: ['user', 'user.userRoles'],
      });
    });
  }

  async findAllExperienceOptions(): Promise<AgentExperienceOption[]> {
    try {
      return await this.dataSource.getRepository(AgentExperienceOption).find();
    } catch (error) {
      this.logger.error(`Error in findAllExperienceOptions: ${error.message}`);
      throw error;
    }
  }
}
